<?php declare(strict_types = 1);

namespace App\PostType\Brand\Model\Orm\Brand;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\Synchronizable;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Brand\Model\Orm\BrandLocalization\BrandLocalization;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Writer\Model\Orm\WriterLocalization\WriterLocalization;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\JsonContainer;// phpcs:ignore

/**
 * @property int                             $id                      {primary}
 * @property string|null                     $extId                   {default null}
 * @property string                          $internalName            {default ''}
 * @property ArrayHash                       $customFieldsJson        {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<BrandLocalization>  $localizations           {1:m BrandLocalization::$brand}
 * @property ParameterValue|null             $parameterValueBrand    {1:1 ParameterValue::$brand, isMain=true}
 *
 * VIRTUAL
 * @property ArrayHash|null                  $cf                      {virtual}
 */
class Brand extends BaseEntity implements ParentEntity, Synchronizable
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	/**
	 * @return ICollection<BrandLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): BrandLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof BrandLocalization);
		return $localization;
	}

	public function getExternalId(): null|string
	{
		return $this->extId;
	}

	public function setExternalId(?string $externalId): void
	{
		$this->extId = (string) $externalId;
	}
}
