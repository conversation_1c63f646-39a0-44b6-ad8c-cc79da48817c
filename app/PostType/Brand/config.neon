application:
	mapping:
		Brand: App\PostType\Brand\*Module\Presenters\*Presenter

cf:
	templates:
		brandLocalization:
			settings:
				type: group
				label: "Nastavení"
				items:
					hideFilters:
						type: checkbox
						label: "Schovat filtry"
			catalog_header:
				extends: @cf.definitions.header
				label: "Nastavení záhlaví"
			pageBanner:
				type: group
				label: "Bannery"
				items:
					banners:
						extends: @cf.definitions.bannerChoose
			# parameterForFilter:
			# 	type: group
			# 	label: "Nastavení filtrů"
			# 	items:
			# 		specialFilters:
			# 			type: group
			# 			label: "Speciálni filtry"
			# 			items:
			# 				price:
			# 					type: group
			# 					label: "Cena"
			# 					items:
			# 						sort:
			# 							type: text
			# 							label: 'Pořadí'
			# 						opened:
			# 							type: checkbox
			# 							label: 'Rozbalené ve výchozim stavu'
			# 						hide:
			# 							type: checkbox
			# 							label: 'Schovat'

			# 					order: 1
			# 				showOnly:
			# 					type: group
			# 					label: "Zobrazit pouze"
			# 					items:
			# 						sort:
			# 							type: text
			# 							label: 'Pořadí'
			# 						opened:
			# 							type: checkbox
			# 							label: 'Rozbalené ve výchozim stavu'
			# 						hide:
			# 							type: checkbox
			# 							label: 'Schovat'
			# 					order: 2

			# 		visibleFilters:
			# 			type: text
			# 			label: 'Počet rozbalených filtrů ve výchozím stavu'
			# 		visibleParameters:
			# 			type: list
			# 			label: Parametry
			# 			items:
			# 				indexable:
			# 					type: checkbox
			# 					label: "Indexovatelné"
			# 				visibleCount:
			# 					type: text
			# 					label: "Počet viditelných hodnot"
			# 				parameter:
			# 					type: suggest
			# 					subType: parameter
			# 					placeholder: Jméno parametru
			# 					url: @cf.suggestUrls.searchParameterForFilter
			# 				tooltip:
			# 					type: tinymce
			# 					label: "Tooltip"
			# 				numberAsRange:
			# 					type: checkbox
			# 					label: "Rozsah pro číselné hodnoty"
			# 				link:
			# 					type: suggest
			# 					subType: tree
			# 					label: 'Odkaz pro zobrazení všech'
			# 					url: @cf.suggestUrls.searchMutationPage
			# 				opened:
			# 					type: checkbox
			# 					label: 'Rozbalené ve výchozim stavu'
						
parameters:
	postTypeRoutes:
		Brand: brand

services:
	- App\PostType\Brand\AdminModule\Components\BrandDetailForm\BrandDetailFormPrescription
	- App\PostType\Brand\AdminModule\Components\BrandDataGrid\BrandDataGridPrescription

	- App\PostType\Brand\Model\BrandLocalizationFacade
	- App\PostType\Brand\Model\Orm\BrandLocalization\BrandLocalizationModel
	- App\PostType\Brand\FrontModule\Components\Alphabet\AlphabetComponentFacade


