<?php

declare(strict_types = 1);

namespace App\PostType\Banner\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasConsts;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\JsonContainer; // phpcs:ignore

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property string $type {enum self::TYPE_*} {default self::TYPE_SINGLE_SLOT}
 * @property ArrayHash $customFieldsJson {container JsonContainer}

 *
 * RELATIONS
 * @property OneHasMany<BannerLocalization> $localizations {1:M BannerLocalization::$banner}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
class Banner extends BaseEntity implements ParentEntity
{

	use HasCustomFields;
	use HasConsts;

	public const string TYPE_SINGLE_SLOT = 'single_slot';
	public const string TYPE_DOUBLE_SLOT = 'double_slot';

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	/**
	 * @return ICollection<BannerLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): BannerLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof BannerLocalization);
		return $localization;
	}

}
