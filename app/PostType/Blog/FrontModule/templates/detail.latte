{varType App\PostType\Blog\Model\Orm\BlogLocalization $object}

{block content}
	<div class="row-main">
		{php $isLocked = $userEntity == null} {* TODO článek pouze pro premium *}

		{include $templates.'/part/box/header-article.latte'}
		{control customContent<PERSON><PERSON>er, [ccClass: 'u-maw-8-12 u-mx-auto', isLocked: $isLocked]}

		<div class="u-maw-8-12 u-mx-auto">
			{* Štítky *}
			<hr class="tw-mt-[3.2rem] tw-mb-[2rem] md:tw-mb-[3.2rem]">
			<div n:if="$object->blogTags->count() > 0" class="tw-flex tw-flex-wrap tw-gap-[0.4rem] tw-items-center tw-mb-[1.2rem]">
				<p class="u-mb-0 u-fw-b tw-text-[1.4rem] nd:tw-text-[1.5rem]">{_"article_tags"}</p>
				{include $templates.'/part/crossroad/tags.latte', link: true, noStyle: true, class: 'tw-contents tw-mb-[0.4rem] md:tw-mb-[1rem] tw-col-start-1 tw-col-end-3 md:tw-col-end-1', crossroad: $object->blogTags}
			</div>

			{* Poslední datum aktualizace *}
			<p class="u-ta-r u-c-help tw-text-[1.3rem] md:tw-text-[1.4rem] tw-mb-[1.2rem]">{_"article_last_update"} {$object->editedTime|date:"j. n. Y"}</p>

			{* Autoři *}
			{foreach $object->authors as $author}
				{include $templates.'/part/box/course-lecturer.latte', class: 'tw-mb-[2rem] md:tw-mb-[3.2rem]', id: 'author'.$iterator->counter, img: isset($author->getParent()->cf->settings->avatar) ? $author->getParent()->cf->settings->avatar->getEntity() ?? false : false, name: $author->name, position: $author->cf->settings??->position ?? false, text: $author->cf->settings??->bio ?? false, link: '#', btnLang: 'btn_author_articles'}
			{/foreach}

			{* Hodnocení *}
			{control likeDislike}
		</div>
		<hr class="u-mb-sm u-mb-2xl@md">
		<div class="u-mb-md u-mb-2xl@md">
			<div class="grid grid--y-xs grid--x-sm">
				<div class="grid__cell size--6-12@lg size--7-12@xl size--8-12@xxl">
					{control newsletterForm, [class: 'f-newsletter--article', type: 'article']}
				</div>
				<div class="grid__cell size--6-12@lg size--5-12@xl size--4-12@xxl">
					{include $templates.'/part/menu/social.latte', class: 'm-social--bg', titleLang: 'socials_article_title'}
				</div>
			</div>
		</div>
		<p class="u-mb-md u-mb-2xl@md">
			{control productsParsedFromPage}
		</p>
		<p class="u-mb-md u-mb-2xl@md">
			{include $templates.'/part/crossroad/articles-carousel.latte', title: title_more_articles, articles: $moreArticles, gradient: true}
		</p>
	</div>
{/block}