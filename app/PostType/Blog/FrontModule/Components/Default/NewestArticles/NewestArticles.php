<?php

namespace App\PostType\Blog\FrontModule\Components\Default\NewestArticles;

use App\Model\Orm\Mutation\Mutation;
use App\Model\TranslatorDB;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
class NewestArticles extends Control
{
    public function __construct(
        private Mutation $mutation,
        private TranslatorDB $translatorDB,
        private BlogLocalizationRepository $blogLocalizationRepository,
    ) {
    }

	public function render(): void
	{
        $newestBlogLocalizations = $this->blogLocalizationRepository
            ->findBy([
                'mutation' => $this->mutation,
            ])
            ->limitBy(7)
            ->orderBy('publicFrom', 'DESC');

		$this->template
            ->setTranslator($this->translatorDB)
            ->render(__DIR__ . '/newestArticles.latte', [
                'newestBlogLocalizations' => $newestBlogLocalizations,
                'templates' => FE_TEMPLATE_DIR,
                'loadMore' => $this->mutation->pages->dronzoneNews ?? false
            ]);
	}
}
