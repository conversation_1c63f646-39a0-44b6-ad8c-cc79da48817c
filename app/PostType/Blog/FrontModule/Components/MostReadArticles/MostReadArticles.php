<?php 

declare(strict_types = 1);

namespace App\PostType\Blog\FrontModule\Components\MostReadArticles;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Time\CurrentDateTimeProvider;
use App\Model\TranslatorDB;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Page\Model\Orm\CommonTree;
use Carbon\CarbonImmutable;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
class MostReadArticles extends Control
{
    
    public function __construct(
        private readonly CommonTree $object,
        private readonly Orm $orm,
        private readonly Mutation $mutation,
        private readonly array $blogLocalizationsByCategoryIds,
        private readonly TranslatorDB $translator,
        private readonly CurrentDateTimeProvider $currentDateTimeProvider,
    ) {
    }
    
    public function render(): void
    {
        $this->template
            ->setTranslator($this->translator)
            ->render(__DIR__ . '/mostReadArticles.latte', [
                'articles' => $this->getArticles(),
                'object' => $this->object,
            ]);
    }

    /**
     * @return ICollection<BlogLocalization>
     */
    protected function getArticles(): ICollection
    {
        $daysAgo = $this->mutation->pages?->blog?->cf?->blogMostReadArticlesDays?->blogMostReadArticlesDays ?? 4;

        $now = CarbonImmutable::parse($this->currentDateTimeProvider->getCurrentDateTime());

        $articles = $this->orm->blogLocalization->findBy([
                'mutation' => $this->mutation,
                'id' => $this->blogLocalizationsByCategoryIds,
                'publicFrom>' => $now->subDays($daysAgo),
            ])
            ->orderBy('viewsNumber', 'DESC')
            ->limitBy(4);

        return $articles;
    }

}


