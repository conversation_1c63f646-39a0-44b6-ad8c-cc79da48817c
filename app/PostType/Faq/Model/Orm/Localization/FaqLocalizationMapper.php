<?php

declare(strict_types = 1);

namespace App\PostType\Faq\Model\Orm\Localization;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use App\PostType\Page\Model\Orm\CommonTree;
use App\Support\Orm\Contracts\Mapper\SearchableMapperContract;
use App\Support\Orm\Traits\Mapper\SearchableMapper;
use Nextras\Dbal\QueryBuilder\QueryBuilder;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<FaqLocalization>
 * @implements SearchableMapperContract<FaqLocalization>
 */
class FaqLocalizationMapper extends DbalMapper implements SearchableMapperContract
{

	use HasCamelCase;
	/** @phpstan-use SearchableMapper<FaqLocalization> */
	use SearchableMapper;

	protected array $searchableFields = ['name'];

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
		return 'faq_localization';
	}

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);

		return $conventions;
	}

	/**
	 * @return ICollection<FaqLocalization>
	 */
	public function findByIdInPathString(CommonTree $commonTree): ICollection
	{
		$builder = $this->builder()
			->andWhere('pathString LIKE %_like_', '|' . $commonTree->id . '|');

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<FaqLocalization>
	 */
	public function findFiltered(array $ids): ICollection
	{
		$builder = $this->builder()
			->select('p.*')
			->from($this->getTableName(), 'p')
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}


	public function findAllIds(?int $limit): Result
	{
		$builder = $this->builder()
			->select('gl.id')
			->from($this->getTableName(), 'gl')
			->limitBy($limit);

		return $this->connection->queryByQueryBuilder($builder);
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit): Result
	{
		$builder = $this->preBuilder($mutation)
			->select('gl.id')
			->from($this->getTableName(), 'gl')
			->limitBy($limit);

		return $this->connection->queryByQueryBuilder($builder);
	}

	/**
	 * @return ICollection<FaqLocalization>
	 */
	public function getPaginatedItems(int $offset, int $limit, CommonTree $commonTree, Mutation $mutation): ICollection
	{
		$builder = $this->preBuilder($mutation)
			->select('gl.*')
			->andWhere('gl.treeId = %i', $commonTree->id)
			->limitBy($limit, $offset);

		return $this->toCollection($builder);
	}

	public function getItemsCount(CommonTree $commonTree, Mutation $mutation): int
	{
		$builder = $this->preBuilder($mutation)
			->select('COUNT(*)')
			->from($this->getTableName(), 'gl')
			->andWhere('gl.treeId = %i', $commonTree->id);

		return $this->connection->queryByQueryBuilder($builder)->fetchField();
	}

	/**
	 * @return ICollection<FaqLocalization>
	 */
	public function getPublicItemsInCategory(CommonTree $commonTree, Mutation $mutation): ICollection
	{
		$builder = $this->preBuilder($mutation)
			->select('gl.*')
			->andWhere('gl.treeId = %i', $commonTree->id);

		return $this->toCollection($builder);
	}

	public function preBuilder(Mutation $mutation): QueryBuilder
	{
		$builder = $this->builder()
			->from($this->getTableName(), 'gl');

		$entityClassName = $this->getRepository()->getEntityClassName([]);

		if (is_a($entityClassName, Publishable::class, true)) {
			$builder->andWhere('gl.public = 1');
		}

		if (is_a($entityClassName, Validatable::class, true)) {
			$now = new \DateTimeImmutable('now');
			$builder->andWhere('gl.publicFrom <= %dt', $now);
			$builder->andWhere('gl.publicTo >= %dt', $now);
		}

		return $builder
			->andWhere('gl.mutationId = %i', $mutation->id);
	}

}
