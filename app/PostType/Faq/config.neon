parameters:
	postTypeRoutes:
		Faq: faq

cf:
	templates:
		faqLocalization:
			settings:
				type: group
				label: "Nastavení"
				items:
					question:
						type: text
						label: "Ot<PERSON><PERSON><PERSON>"
						required: true
					answer:
						label: "Od<PERSON>věď"
						required: true
						type: tinymce
						tiny-type: lite

application:
	mapping:
		Faq: App\PostType\Faq\*Module\Presenters\*Presenter

services:
	- App\PostType\Faq\AdminModule\Components\Form\FormData\FaqFormData
	- App\PostType\Faq\AdminModule\Components\Form\FaqFormPrescription
	- App\PostType\Faq\Model\FaqLocalizationFacade
	- App\PostType\Faq\FrontModule\Components\FaqFormComponentFactory
