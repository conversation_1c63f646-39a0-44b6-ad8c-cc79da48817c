parameters:
	postTypeRoutes:
		ClientTestimonial: client-testimonial

cf:
	templates:
		clientTestimonialLocalization:
			settings:
				type: group
				label: "Nastavení"
				items:
					imgPerson:
						type: image # has size
						label: "Fotografie osoby (min. 150x150)"
					imgCompany:
						type: image # has size
						label: "<PERSON><PERSON> (min. 150x150)"
					text:
						type: textarea
						label: "Text"
					info:
						type: text
						label: "Pozice / společnost"

application:
	mapping:
		ClientTestimonial: App\PostType\ClientTestimonial\*Module\Presenters\*Presenter

services:
	- App\PostType\ClientTestimonial\AdminModule\Components\Form\FormData\ClientTestimonialFormData
	- App\PostType\ClientTestimonial\AdminModule\Components\Form\ClientTestimonialFormPrescription
	- App\PostType\ClientTestimonial\Model\ClientTestimonialLocalizationFacade
