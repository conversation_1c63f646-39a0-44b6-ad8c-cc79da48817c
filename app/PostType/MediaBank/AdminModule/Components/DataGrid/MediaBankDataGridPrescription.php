<?php declare(strict_types = 1);

namespace App\PostType\MediaBank\AdminModule\Components\DataGrid;

use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use Ublaboo\DataGrid\DataGrid;
use Ublaboo\DataGrid\Filter\FilterSelect;

readonly class MediaBankDataGridPrescription
{

	public function get(): DataGridDefinition
	{
		$datagrid = new DataGrid();
		$datagrid->setPagination(true);
		$datagrid->setSortable();
		$datagrid->setSortableHandler('mediaBankSort!');

		$extender = new CustomDataGridExtender(function (DataGrid $dataGrid) {
			$dataGrid->getColumn('name')->setSortable(false);
			$dataGrid->removeFilter('name');

			$dataGrid->addColumnText('type', 'type')
				->setRenderer(function ($entity): string {
					return $entity->getParent()->getType()->name;
				});

			if (isset($dataGrid->getColumns()['mutation'])) {
				$mutationFilter = $dataGrid->getFilter('mutation');
				assert($mutationFilter instanceof FilterSelect);
				$mutationFilter->setPrompt(null);
				$dataGrid->setDefaultFilter(['mutation' => 1]);
			}
		});

		return new DataGridDefinition(
			dataGrid: $datagrid,
			beforeRenderExtenders: [$extender]
		);
	}

}
