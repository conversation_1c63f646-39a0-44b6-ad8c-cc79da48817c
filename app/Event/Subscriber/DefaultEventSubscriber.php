<?php

declare(strict_types = 1);

namespace App\Event\Subscriber;

use App\Model\Messenger\Erp\Sync\SyncMessage;
use App\Model\Odoo\Endpoint\PartnerEndpoint;
use App\Model\Odoo\Entity\Partner;
use App\Model\Orm\Orm;
use App\Model\Orm\User\Event\Created\UserCreated;
use App\Model\Orm\User\Event\Updated\UserUpdated;
use App\Model\Orm\User\Event\Updated\UserUpdatedCustomAddress;
use App\Model\Orm\User\Event\Updated\UserUpdatedInvoiceAddress;
use Nette\Utils\ArrayHash;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

readonly final class DefaultEventSubscriber implements EventSubscriberInterface
{

	public function __construct(
		private MessageBusInterface $messageBus,
		private Orm $orm,
	)
	{
	}

	public function onUserCreated(UserCreated $event): void
	{
		if ($event->apiRequest) {
			trigger_error('User created via API, skipping Odoo sync.', E_USER_NOTICE);
			return;
		}

		$odooEntity = new Partner($event->user);
		$odooEndpoint = new PartnerEndpoint($odooEntity);
		$this->messageBus->dispatch(new SyncMessage($odooEndpoint));
	}

	public function onUserUpdated(UserUpdated $event): void
	{
		$odooEntity = new Partner($event->user);
		$odooEndpoint = new PartnerEndpoint($odooEntity);
		$this->messageBus->dispatch(new SyncMessage($odooEndpoint));
	}

	public function onUserUpdatedInvoiceAddress(UserUpdatedInvoiceAddress $event): void
	{
		$hasCompany = $event->user->company !== null && strlen(trim($event->user->company)) > 0;

		if ($event->user->customAddress === null || count($event->user->customAddress) === 0) {
			$event->user->customAddress = [
				[
					'addressTitle' => 'Default',
					'invFirstname' => $event->user->firstname,
					'invLastname' => $event->user->lastname,
					'invStreet' => $event->user->street,
					'invCity' => $event->user->city,
					'invZip' => $event->user->zip,
					'invState' => $event->user->state->id,
					'invPhone' => $event->user->phone,
					'invCompany' => $hasCompany ? $event->user->company : null,
					'invIc' => $hasCompany ? $event->user->ic : null,
					'invDic' => $hasCompany ? $event->user->dic : null,
					'isDefault' => true,
					'delFirstname' => $event->user->firstname,
					'delLastname' => $event->user->lastname,
					'delStreet' => $event->user->street,
					'delCity' => $event->user->city,
					'delZip' => $event->user->zip,
					'delState' => $event->user->state->id,
					'delPhone' => $event->user->phone,
				],
			];

		} else {
			$customAddresses = [];
			$i = 1;
			foreach ($event->user->customAddress as $customAddress) {
				$customAddress = ArrayHash::from((array) $customAddress);
				if (!isset($customAddress->addressTitle)) {
					$customAddress->addressTitle = 'Adresa č.' . $i;
				}
				$customAddress['invFirstname'] = $event->user->firstname;
				$customAddress['invLastname'] = $event->user->lastname;
				$customAddress['invStreet'] = $event->user->street;
				$customAddress['invCity'] = $event->user->city;
				$customAddress['invZip'] = $event->user->zip;
				$customAddress['invState'] = $event->user->state->id;
				$customAddress['invPhone'] = $event->user->phone;
				$customAddress['invCompany'] = $hasCompany ? $event->user->company : null;
				$customAddress['invIc'] = $hasCompany ? $event->user->ic : null;
				$customAddress['invDic'] = $hasCompany ? $event->user->dic : null;
				$customAddresses[] = $customAddress;
				$i++;
			}
			$event->user->customAddress = $customAddresses;
		}

		if (!$hasCompany) {
			$event->user->company = null;
			$event->user->ic = null;
			$event->user->dic = null;
		}

		$this->orm->user->persistAndFlush($event->user);
	}

	public function onUserUpdatedCustomAddress(UserUpdatedCustomAddress $event): void
	{
		// set invoice address from custom address
		bdump($event->user, 'UserUpdatedCustomAddress');
	}

	public static function getSubscribedEvents(): array
	{
		return [
			UserCreated::class => [
				['onUserCreated', 1],
			],
			UserUpdated::class => [
				['onUserUpdated', 1],
			],
			UserUpdatedInvoiceAddress::class => [
				['onUserUpdatedInvoiceAddress', 10],
			],
			UserUpdatedCustomAddress::class => [
				['onUserUpdatedCustomAddress', 11],
			],
		];
	}

}
