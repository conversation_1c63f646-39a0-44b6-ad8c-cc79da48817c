<?php declare(strict_types=1);

namespace App\Model\BucketFilter\SetupCreator\Brand;

use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\PostType\Brand\Model\Orm\BrandLocalization\BrandLocalization;

interface ElasticItemListFactory
{

	public function create(
		BrandLocalization $parameterObject,
		State $currentState,
		PriceLevel $priceLevel,
		array $allSelectedParameters,
	): ElasticItemList;

}
