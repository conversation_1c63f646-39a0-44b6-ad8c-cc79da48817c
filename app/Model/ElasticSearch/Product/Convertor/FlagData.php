<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Product\Product;
use App\PostType\Tag\Model\TagType;


class FlagData implements Convertor
{
	const string IS_NEW = 'isNew';

	public function convert(Product $product): array
	{
		return [
			'tags' => $product->tags->toCollection()->fetchPairs(null, 'id'),
			self::IS_NEW => $product->hasTag(TagType::new),
			'isOld' => (bool) $product->isOld,
			'isBestseller' => $product->hasTag(TagType::bestseller),
			'isTop' => $product->hasTag(TagType::top),
		];
	}

}
