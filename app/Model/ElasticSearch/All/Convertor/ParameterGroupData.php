<?php 

declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\PostType\ParameterGroup\Model\Orm\Localization\ParameterGroupLocalization;

class ParameterGroupData implements Convertor
{

	public function convert(object $object): array
	{
		assert($object instanceof ParameterGroupLocalization);
		$ret = [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => 'parameterGroup',
			'langCode' => $object->mutation->langCode,
			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
		];
		$ret['kind'] = 'parameterGroup';

		return $ret;
	}

}
