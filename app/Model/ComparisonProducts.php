<?php
declare(strict_types=1);
namespace App\Model;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use Nette\Http\Request;
use Nette\Http\Response;
use Nette\Utils\Json;
use Throwable;

final class ComparisonProducts
{
	const COOKIE_NAME = 'comparisonProducts';
	const COOKIE_EXPIRE = '+3 months';

	private ?array $list = null;

	private int $maxCount = 100;

	public function __construct(
		private readonly Request $httpRequest,
		private readonly Response $httpResponse,
		private readonly ProductRepository $productRepository,
		ConfigService $configService,
	)
	{
		$this->maxCount = $configService->getParam('shop', 'comparisonProductsCount');
	}

	public function add(Product $product): void
	{
		$this->addToAll($product);
		$this->addToCategory($product, $product->getCategoryForCompare()->getAlias());
		$this->invalidateList();
	}

	public function remove(Product $product): void
	{
		$this->removeFromAll($product);
		$this->removeFromCategory($product, $product->getCategoryForCompare()->getAlias());
		$this->invalidateList();
	}

	public function addToAll(Product $product): void
	{
		$list = $this->getList();

		if (! isset($list['all'])) {
			$list['all'] = [];
		}

		if ($product->id) {

			if (! in_array($product->id, $list['all'] ?? [])) {
				array_unshift($list['all'], $product->id);
			}

			$this->save($list);
		}
	}

	public function removeFromAll(Product $product): void
	{
		$list = $this->getList();

		if (! isset($list['all'])) {
			$list['all'] = [];
		}

		if ($product->id) {
			if (in_array($product->id, $list['all'] ?? [])) {
				unset($list['all'][array_search($product->id, $list['all'])]);
			}

			$this->save($list);
		}
	}

	public function addToCategory(Product $product, string $categoryAlias): void
	{
		$list = $this->getList();

		if (! isset($list[$categoryAlias])) {
			$list[$categoryAlias] = [];
		}

		if ($product->id) {
			if (! in_array($product->id, $list[$categoryAlias] ?? [])) {
				array_unshift($list[$categoryAlias], $product->id);
			}

			$this->save($list);
		}
	}

	public function removeFromCategory(Product $product, string $categoryAlias): void
	{
		$list = $this->getList();

		if (! isset($list[$categoryAlias])) {
			$list[$categoryAlias] = [];
		}

		if ($product->id) {
			if (in_array($product->id, $list[$categoryAlias] ?? [])) {
				unset($list[$categoryAlias][array_search($product->id, $list[$categoryAlias])]);
			}

			if ($list[$categoryAlias] === []) {
				unset($list[$categoryAlias]);
			}

			$this->save($list);
		}
	}

	public function getAllProductIds(): array
	{
		$list = $this->getList();

		return $list['all'] ?? [];
	}

	public function getCategoryProductIds(string $categoryAlias): array
	{
		$list = $this->getList();

		return $list[$categoryAlias] ?? [];
	}

	public function getCount(): int
	{
		return count($this->getAllProductCollection());
	}

	public function getAllProductCollection(): array
	{
		$productIds = $this->getAllProductIds();

		if ($productIds === []) {
			return [];
		}

		return $this->getProductsByIds($productIds);
	}

	public function getCategoryProductCollection(string $categoryAlias): array
	{
		$productIds = $this->getCategoryProductIds($categoryAlias);
		if ($productIds === []) {
			return [];
		}
		
		return $this->getProductsByIds($productIds);
	}

	protected function getProductsByIds(array $productIds): array
	{
		$products = $this->productRepository
			->findByIdOrder($productIds)
			->limitBy($this->maxCount)
			->fetchAll();

		foreach ($products as $product) {
			assert($product instanceof Product);
			if ($product->activeVariants->count() === 0 || !$product->isPublic()) {
				$this->remove($product);
				unset($products[array_search($product, $products)]);
			}
		}

		return $products;
	}

	public function getList(): array
	{
		if ($this->list === null) {
			$this->list = $this->loadCookie();
		}

		return $this->list;
	}

	private function loadCookie(): array
	{
		try {
			$value = $this->httpRequest->getCookie(self::COOKIE_NAME);

			if($value !== null){
				return Json::decode($value, forceArrays: true);
			}

		} catch (Throwable $e) {

		}
		return [];
	}

	public function isProductInComparison(int $productId): bool
	{
		return in_array($productId, $this->getAllProductIds());
	}

	private function save(array $list): void
	{
		$this->list = $list;
		$this->httpResponse->setCookie(self::COOKIE_NAME, Json::encode($list), self::COOKIE_EXPIRE);
	}

	public function clear(): void
	{
		$this->list = [];
		$this->save([]);
	}

	public function moveItemInCategory(int $index, string $direction, string $categoryAlias): void
	{
		$list = $this->getList();
		$productIds = array_values($list[$categoryAlias]);
		$count = count($productIds);

		if ($count < 2 || $index < 0 || $index >= $count) {
			return;
		}

		if ($direction === 'up') {
			if ($index === 0) {
				$item = array_shift($productIds);
				array_push($productIds, $item);
			} else {
				[$productIds[$index - 1], $productIds[$index]] = [$productIds[$index], $productIds[$index - 1]];
			}
		} elseif ($direction === 'down') {
			if ($index === $count - 1) {
				$item = array_pop($productIds);
				array_unshift($productIds, $item);
			} else {
				[$productIds[$index + 1], $productIds[$index]] = [$productIds[$index], $productIds[$index + 1]];
			}
		} else {
			return;
		}

		$list[$categoryAlias] = array_values($productIds);
		
		$this->save($list);
	}

	private function invalidateList(): void
	{
		$list = $this->getList();

		if (! isset($list['all']) || ! is_array($list['all'])) {
			$this->save([]);
			return;
		}

		$all = array_slice($list['all'] ?? [], 0, $this->maxCount);

		foreach ($list as $key => $value) {
			if (! is_array($value)) {
				$list[$key] = [];
				continue;
			}

			$list[$key] = array_intersect($value, $all);
		}

		$this->save($list);
	}
}
