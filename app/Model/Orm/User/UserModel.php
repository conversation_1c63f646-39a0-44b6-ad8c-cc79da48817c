<?php declare(strict_types = 1);

namespace App\Model\Orm\User;

use App\Model\CacheFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Delivery\DeliveryType;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\State;
use App\Model\Orm\User\Event\Created\UserCreated;
use App\Model\Orm\User\Event\Updated\UserUpdated;
use App\Model\Orm\User\Event\Updated\UserUpdatedCustomAddress;
use App\Model\Orm\User\Event\Updated\UserUpdatedInvoiceAddress;
use App\Model\Orm\UserHash\UserHash;
use App\Model\Orm\UserImage\UserImage;
use App\Model\Security\Acl;
use Brick\Math\BigDecimal;
use Nette\Caching\Cache;
use Nette\Security\Passwords;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use App\Model\CustomField\CustomFields;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

final class UserModel
{
	private Cache $cache;

	public function __construct(
		private readonly Orm $orm,
		private readonly CustomFields $customFields,
		private readonly Passwords $passwords,
		private readonly EventDispatcherInterface $eventDispatcher,
		CacheFactory $cacheFactory,
	) {
		$this->cache = $cacheFactory->create(self::class);
	}

	public function createClubBonusTexts(User $user): array
	{
		if ($user->prefferedDelivery !== null) {
			if (in_array($user->prefferedDelivery->getDeliveryMethod()->getDeliveryType(), [DeliveryType::Pickup, DeliveryType::Physical])) {
				return ['product_benefit_family', 'product_benefit_dispatch', 'product_benefit_delivery'];
			}
		}

		return ['product_benefit_personal', 'product_benefit_family'];
	}

	public function delete(User $user): void
	{
		$this->orm->userHash->removeByUser($user);

		$this->orm->removeAndFlush($user);
	}

	public function createFromOrder(Order $order, mixed $values): User
	{
		$customAddress = [];

		$values->email = $order->email;
		$values->phone = $customAddress['invPhone'] = $order->phone;
		$values->street = $customAddress['invStreet'] = $order->street;
		$values->city = $customAddress['invCity'] = $order->city;
		$values->zip = $customAddress['invZip'] = $order->zip;
		$values->state = $order->country;

		$customAddress['invState'] = $order->country->id;

		$splitName = explode(' ', $order->name);
		$firstName = $splitName[0];
		unset($splitName[0]);
		$lastName = implode(' ', $splitName);
		$values->firstname = $customAddress['invFirstname'] = $firstName;
		$values->lastname = $customAddress['invLastname'] = $lastName;

		$customAddress['invCompany'] = $order->companyName;
		$customAddress['invIc'] = $order->companyIdentifier;
		$customAddress['invDic'] = $order->vatNumber;

		$values->customAddress = [$customAddress];


		$user = new User();
		$this->orm->user->attach($user);
		$user->role = Acl::ROLE_USER;
		$user->priceLevel = PriceLevel::DEFAULT_ID;
		$user->mutations->add($order->mutation);

		$user = $this->save($user, (array) $values);

		if ($user->isPersisted()) {
			$order->user = $user;
			$this->orm->order->persistAndFlush($order);
		}

		$this->eventDispatcher->dispatch(new UserCreated($user));

		return $user;
	}

	public function createFromImport(ArrayHash $importData, Mutation $mutation): User
	{
		/** @var State $state */
		$state = $this->orm->state->getDefault($mutation);
		$customAddress = [];

		$values = [];
		$values['email'] =  $importData->email;
		$values['extId'] = $importData->id;
		$values['discountPercent'] = BigDecimal::of($importData->discount_percent);
		$values['oldPasswordHash'] = $importData->password;
		$values['oldPasswordSalt'] = $importData->password_salt;
		$values['extLists'] = $importData->lists;

		$values['phone'] = $customAddress['invPhone'] = $importData->phone_number;
		$values['firstname'] = $customAddress['invFirstname'] = Strings::substring($importData->name ?? '', 0, 120) ;
		$values['lastname'] = $customAddress['invLastname'] = Strings::substring($importData->sname ?? '', 0, 120);

		if (isset($importData->customer->invoice_address)) {
			$values['street'] = $customAddress['invStreet'] = Strings::substring($importData->customer->invoice_address->street, 0, 250);
			$values['city']   = $customAddress['invCity'] = Strings::substring($importData->customer->invoice_address->city, 0, 120);
			$values['zip']    = $customAddress['invZip'] = Strings::substring($importData->customer->invoice_address->zip_code, 0, 20);

			$customAddress['invCompany'] = $importData->customer->invoice_address->company ?? null;
			$customAddress['invIc'] = $importData->customer->invoice_address->ic ?? null;
			$customAddress['invDic'] = $importData->customer->invoice_address->dic ?? null;
		}

		if(isset($importData->customer->delivery_address)) {
			$customAddress['delStreet'] = $importData->customer->delivery_address->street;
			$customAddress['delCity'] = $importData->customer->delivery_address->city;
			$customAddress['delZip'] = $importData->customer->delivery_address->zip_code;
			$customAddress['delFirstname'] = $customAddress['invFirstname'];
			$customAddress['delLastname'] = $customAddress['invLastname'];

			if (strlen(trim($importData->customer->delivery_address->name ?? '')) > 0) {
				$customAddress['delFirstname'] = $importData->customer->delivery_address->name;
			}

			if (strlen(trim($importData->customer->delivery_address->sname ?? '')) > 0) {
				$customAddress['delLastname'] = $importData->customer->delivery_address->sname;
			}

			$customAddress['delCountry'] = $state->id;
		}

		$values['state'] = $state;

		$customAddress['invState'] = $state->id;

		$values['customAddress'] = [$customAddress];

		try {
			$values['createdTime'] = new DateTimeImmutable($importData->date_created);
		} catch (\Throwable $e) {
			// do nothing
		}

		$values['payWithInvoice'] = $importData->allow_on_invoice;
		$values['freeTransit'] = $importData->free_delivery;

		$user = new User();
		$this->orm->user->attach($user);
		$user->role = Acl::ROLE_USER;
		$user->priceLevel = PriceLevel::DEFAULT_ID;
		$user->mutations->add($mutation);

		return $this->save($user, $values);
	}

	public function saveInvoiceAddress(User $user, mixed $data, ?int $userId = null): User
	{
		$user = $this->save($user, $data, $userId);
		$this->eventDispatcher->dispatch(new UserUpdatedInvoiceAddress($user));

		return $user;
	}

	public function saveCustomAddress(User $user, mixed $data, ?int $userId = null): User
	{
		$user = $this->save($user, $data, $userId);
		$this->eventDispatcher->dispatch(new UserUpdatedCustomAddress($user));

		return $user;
	}

	public function save(User $user, mixed $data, ?int $userId = null, string $action = 'edit'): User
	{
		// tyto hodnoty jsou bool a DB je chce v integeru
		$intValues = [];
		$boolValues = ['payWithInvoice', 'freeTransit', 'isClubMember']; //'showPriceMy', 'showPriceBrutto', 'isBlock'
		$noSave = ['id', 'userMutations'];

		if (($data['password'] ?? '') === '') {
			unset($data['password']);
			unset($data['passwordVerify']);
		}

//		if (isset($data['preferLang']) && $data['preferLang'] && $user->preferLang != $data['preferLang']) {
//			$this->response->setCookie(LangDetector::COOKIE_KEY, 0, '300 days', null, null, true, true);
//		}

		if (($data['ic'] ?? '') !== '') {
			$data['ic'] = mb_substr($data['ic'], 0, 20);
		}

		if (($data['dic'] ?? '') !== '') {
			$data['dic'] = mb_substr($data['dic'], 0, 20);
		}

		if ($action === 'create') {
			if ($userId) {
				$data['created'] = $userId;
			}

			$data['createdTime'] = new DateTimeImmutable();

		} else {
			if ($userId) {
				$data['edited'] = $userId;
				$data['editedTime'] = new DateTimeImmutable();
			}
		}

		foreach ($user->getMetadata()->getProperties() as $i) {
			$col = $i->name;
			if (isset($data[$col])) {
				if ($col === 'password') {
					if (($data[$col] ?? '') !== '') {
						$data[$col] = $this->passwords->hash($data[$col]);
						$user->setValue($col, $data[$col]);
					} else {
						$user->setValue($col, $user->password);
					}

					continue;
				}

				/** @phpstan-ignore-next-line */
				if (in_array($col, $intValues, true)) {
					$data[$col] = (int) $data[$col];
				}

				if (!in_array($col, $noSave, true)) {
					$user->setValue($col, $data[$col]);
				}
			} else {
				if (in_array($col, $boolValues, true)) {
					$user->setValue($col, 0);
				}
			}
		}

		if ($userId) {
			if (isset($data['customFields'])) {
				$user->cf = $this->customFields->prepareDataToSave($data['customFields']);
			}

			$this->handleMutations($user, $data);
		}

		$this->orm->user->persistAndFlush($user);

		if ($userId !== null) {
			$this->eventDispatcher->dispatch(new UserUpdated($user));
		}

		return $user;
	}

	public function saveEntity(User $user, bool $created = false): User
	{
		$this->orm->user->persistAndFlush($user);
		$this->eventDispatcher->dispatch($created ? new UserCreated($user) : new UserUpdated($user));
		return $user;
	}

	private function handleMutations(User $user, array $data): void
	{
		if (isset($data['userMutations'])) {
			$mutations = [];

			if (!in_array($data['userMutations'], [null, '', []])) {
				foreach ($this->orm->mutation->findBy(['id' => $data['userMutations']]) as $mutation) {
					$mutations[] = $mutation;
				}
			}

			$user->mutations->set($mutations);
		}
	}






	public function deleteHash(User $user, UserHash $hash): void
	{
		$this->orm->userHash->remove($hash);
		$this->orm->user->persistAndFlush($user);
	}


	public function getByEmail(string $email, Mutation $mutation): ?User
	{
		return $this->orm->user->getByEmail($email, $mutation);
	}


	public function create(Mutation $mutation, string $email, ?string $extId = null, bool $apiRequest = false): User
	{
		$newUser = new User();
		$newUser->mutations->add($mutation);
		$newUser->email = $email;
		$newUser->extId = $extId;
		$newUser->password = '';
		$newUser->priceLevel = $this->orm->priceLevel->getDefault();
		$newUser->freeTransit = false;
		$newUser->payWithInvoice = false;
		$this->orm->user->attach($newUser);

		$this->orm->persistAndFlush($newUser);

		$this->eventDispatcher->dispatch(new UserCreated($newUser, $apiRequest));

		return $newUser;
	}

	public function hasOrderedProduct(\App\Model\Security\User $user, Product $product): bool
	{
		return $this->hasOrderedProductById($user, $product->id);
	}
	public function hasOrderedProductById(\App\Model\Security\User $user, int $productId): bool
	{
		$orderedProductIds = $this->findOrderedProductIds($user);
		return in_array($productId, $orderedProductIds);
	}

	public function findOrderedProductIds(\Nette\Security\User $user, ?DateTimeImmutable $orderedFrom = null): array
	{
		return $this->cache->load('orderedProductIds-' . $user->getId() . '-' . ($orderedFrom?->getTimestamp() ?? 0), function (&$dependencies) use ($user/*, $orderedFrom*/) {
			$dependencies[Cache::Expire] = '30 minute';
			$dependencies[Cache::Tags] = ['orderedProductIds-' . $user->getId()];
			return $this->orm->product->findOrderedProductIds($user/*, $orderedFrom*/);
		});
	}

	public function findRelatedOrderedProducts(array $productIds, \DateTimeImmutable $orderFrom):array
	{
		$orderFrom = $orderFrom->setTime(0, 0, 0); // time is used in cache key name

		return $this->cache->load('relatedProductIds-' . md5((string) json_encode($productIds)) . '-' . $orderFrom->getTimestamp(), function (&$dependencies) use ($productIds, $orderFrom) {
			$dependencies[Cache::Expire] = '5 minute';
			$dependencies[Cache::Tags] = ['relatedProductIds'];
			return $this->orm->product->findRelatedOrderedProducts($productIds, $orderFrom);
		});
	}

}
