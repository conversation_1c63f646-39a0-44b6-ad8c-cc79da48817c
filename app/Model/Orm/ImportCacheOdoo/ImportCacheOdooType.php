<?php

declare(strict_types=1);

namespace App\Model\Orm\ImportCacheOdoo;

use App\Model\Messenger\Erp\Country\CountryMessage;
use App\Model\Messenger\Erp\ImportMessage;
use App\Model\Messenger\Erp\Partner\PartnerMessage;
use App\Model\Messenger\Erp\Price\PriceMessage;
use App\Model\Messenger\Erp\ProductCategory\ProductCategoryMessage;
use App\Model\Messenger\Erp\ProductCourse\ProductCourseMessage;
use App\Model\Messenger\Erp\Product\ProductMessage;
use App\Model\Messenger\Erp\ProductTag\ProductTagMessage;
use App\Model\Messenger\Erp\ProductUsp\ProductUspMessage;
use App\Model\Messenger\Erp\Stock\StockMessage;
use App\Model\Odoo\Enum\ModelEnum;

enum ImportCacheOdooType: string {
	case Partner = ModelEnum::Partner->value;
	case Stock = 'stock.level';
	case Price = 'prices';
	case Product = 'product.template';
	case ProductTag = 'product.tag';
	case ProductUsp = 'unique.selling.proposal';
	case ProductCourse = 'slide.channel';
	case Country = 'res.country';
	case ProductCategory = 'product.public.category';
	case Order = 'sale.order';

	public function createImportMessage(?int $importCacheChangeId = null, array $signals = []): ImportMessage
	{
		return match ($this) {
			self::Partner => new PartnerMessage($importCacheChangeId, $signals),
			self::Product => new ProductMessage($importCacheChangeId, $signals),
			self::ProductCourse => new ProductCourseMessage($importCacheChangeId, $signals),
			self::ProductUsp => new ProductUspMessage($importCacheChangeId, $signals),
			self::Country => new CountryMessage($importCacheChangeId, $signals),
			self::Stock => new StockMessage($importCacheChangeId, $signals),
			self::Price => new PriceMessage($importCacheChangeId, $signals),
			self::ProductTag => new ProductTagMessage($importCacheChangeId, $signals),
			self::ProductCategory => new ProductCategoryMessage($importCacheChangeId, $signals),
			default => throw new \InvalidArgumentException('Unknown import cache type: ' . $this->value),
		};
	}
}
