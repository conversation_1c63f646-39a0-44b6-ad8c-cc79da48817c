<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Event\PaymentUnknown;

use App\Model\Orm\Order\Event\OrderEvent;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\Payment\OrderPayment;

final class PaymentUnknown extends OrderEvent
{

	public function __construct(
		public readonly OrderPayment $payment,
	)
	{
	}

	public function getOrder(): Order
	{
		return $this->payment->order;
	}

}
