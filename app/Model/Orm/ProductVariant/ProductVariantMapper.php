<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariant;

use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\User\User;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<ProductVariant>
 */
final class ProductVariantMapper extends DbalMapper
{

	use HasCamelCase;
	use HasStaticCache;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'product_variant';
}



	public function findOrderedProductVariantIds(User $user): array
	{
		return $this->loadCache($this->createCacheKey('orderedProductVariantIds', $user->id), function () use ($user) {
			$builder = $this->builder()->select('DISTINCT [pv.id]')->from('product_variant', 'pv');
			$builder->joinLeft('[order_product] as [op]', '[op.variantId] = [pv.id]');
			$builder->joinLeft('[order] as [o]', '[op.orderId] = [o.id]');
			$builder->where('[o.userId] = %i AND [o.state] NOT IN %s[]', $user->id, [OrderState::Draft, OrderState::Declined, OrderState::Canceled]);

			return $this->connection->queryByQueryBuilder($builder)->fetchPairs(null, 'id');
		});
	}

//	public function getVariantIdsReviewedByUser(User $user, Mutation $mutation, ?string $status = ProductReview::STATUS_APPROVED): ICollection
//	{
//		$builder = $this->builder()->select('pv.*')
//			->from('product_review', 'pr')
//			->joinInner( '[product_variant] as pv', 'pv.productId = pr.productId')
//			->joinInner( '[product_variant_localization] as pvl', 'pvl.variantId = pv.id AND pvl.mutationId = %i', $mutation->id)
//			->joinInner( '[product_localization] as pl', 'pl.productId = pv.productId AND pl.mutationId = %i', $mutation->id)
//			->andWhere('pr.userId = %i', $user->id)
//			->andWhere('pl.public = 1 AND pvl.active = 1')
//			->orderBy('pr.created DESC');
//
//		if (isset($status)) {
//			$builder->andWhere('pr.status = %s', $status);
//		}
//
//		return $this->toCollection($builder);
//	}
}
