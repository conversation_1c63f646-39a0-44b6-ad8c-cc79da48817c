<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariant\Availability;


use App\Model\Orm\Product\Product;

final readonly class ProductAvailabilityService
{
	public function __construct(
		private ProductAvailability $availability
	)
	{
	}

	public function create(Product $product): ProductAvailability
	{
		$productAvailability = clone $this->availability;
		return $productAvailability->setProduct($product);
	}
}
