<?php

declare(strict_types = 1);

namespace App\Model\Orm\Product;

use App\Model\DateTime\DateTimeHelper;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueMapper;
use App\Model\Orm\TableHelper;
use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Time\CurrentDateTimeProvider;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use Nette\Caching\Cache;
use Nette\Utils\DateTime;
use Nette\Utils\Strings;
use Nextras\Dbal\IConnection;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Reflection\PropertyMetadata;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;
use Nextras\Orm\Mapper\Dbal\DbalMapperCoordinator;

/**
 * @extends DbalMapper<Product>
 */
final class ProductMapper extends DbalMapper
{

	use HasCamelCase;
	use HasStaticCache;

	public function __construct(
		private readonly CurrentDateTimeProvider $currentDateTimeProvider,
		IConnection $connection,
		DbalMapperCoordinator $mapperCoordinator,
		Cache $cache)
	{
		parent::__construct($connection, $mapperCoordinator, $cache);
	}

	/**
	* @return literal-string
	*/
	public function getTableName(): string
	{
		return 'product';
	}

	public function getTreeSoldProductsCount(Tree $tree): int
	{
		$halfYearAgo = DateTime::from('now')->modify('-6 m');

		$builder = $this->builder()->select('p.*')->from($this->getTableName(), 'p')
			->joinLeft('[product_variant] as pv', '[p.id] = [pv.productId]')
			->joinLeft('[product_tree] as pt', '[pt.productId] = [pv.productId]')
			->joinLeft('[order_product] as op', '[op.variantId] = [pv.id]')
			->joinLeft('[order] as o', '[o.id] = [op.orderId]')
			->where('pt.treeId = %i', $tree->getId())
			->andWhere('o.placedAt > %dt', $halfYearAgo);

		return count($this->connection->queryByQueryBuilder($builder)->fetchPairs(null, 'id'));
	}

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions); // property is not available on interface
		$conventions->setMapping('linksString', 'links');
		$conventions->setMapping('videosString', 'videos');

		$conventions->manyHasManyStorageNamePattern = '%s_%s';

		return $conventions;
	}

	public function getById(int $productId): Product|null
	{
		$builder = $this->builder()->where('id = %i', $productId);

		$product = $this->connection->queryByQueryBuilder($builder);

		return $this->toEntity($product);
	}


	public function getManyHasManyParameters(PropertyMetadata $sourceProperty, DbalMapper $targetMapper): array
	{
		if ($targetMapper instanceof ParameterValueMapper) {
			return ['product_parameter', ['productId', 'parameterValueId']];
		}

		return parent::getManyHasManyParameters($sourceProperty, $targetMapper);
	}

	// PRODUKTOVE SETY
	/**
	 * @return ICollection<Product>
	 */
	public function findBySetpartsById(int $id): ICollection
	{
		$builder = $this->builder()->select('p.*')->from('product', 'p')
			->joinInner('[product_set] as ps', '[p.id] = [ps.productId] AND [ps.productMainId] = %i',
				$id)->orderBy('ps.sort');
		return $this->toCollection($builder);
	}


	public function addToSet(Product $set, Product $product, int $sort): Result
	{
		return $this->connection->query('INSERT INTO product_set %values', [
			'productMainId' => $set->id,
			'productId' => $product->id,
			'sort' => $sort
		]);
	}


	public function removeFromSet(Product $set, Product $product): Result
	{
		$ret = $this->connection->query('DELETE FROM product_set WHERE productMainId = %i AND productId = %i ', $set->id, $product->id);
		//$this->getRepository()->persistAndFlush($set);
		return $ret;
	}

	/**
	 * @return ICollection<Product>
	 */
	public function findSetBySetpartId(int $id): ICollection
	{
		$builder = $this->builder()->select('p.*')->from('product', 'p')
			->joinInner( '[product_set] as ps', '[p.id] = [ps.productMainId] AND [ps.productId] = %i',
				$id);
		return  $this->toCollection($builder);
	}




	/**
	 * @return ICollection<Product>
	 */
	public function searchByName(string $q, ?array $excluded = null, ?Mutation $mutation = null): ICollection
	{
		$builder = $this->builder()->select('p.*')->from($this->getTableName(), 'p');

		if ($mutation) {
			$builder->joinInner('[product_localization] as pl', 'p.id = pl.productId and pl.mutationId = %i', $mutation->id);
		} else {
			$builder->andWhere('internalName LIKE %_like_', $q);
		}

//		if ($sourceId) {
//			$builder->andWhere('id != %i', $sourceId); // nenabizet ten stejny produkt
//		}

//		$excluded =  [1, 3, 34];
		if ($excluded) {
			$builder->andWhere('p.id NOT IN %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<Product>
	 */
	public function searchBy(string $q, ?array $excluded = null): ICollection
	{
		$builder = $this->builder()->select('p.*')->from($this->getTableName(), 'p');

		$builder->joinInner('[product_variant] as pv', 'p.id = pv.productId');

		 foreach(Strings::split($q, '/\W/') as $queryPart){

			 if (Strings::length($queryPart) <= 1){
				 continue;
			 }

			 $builder->orWhere('pv.ean LIKE %_like_', $queryPart);
			 $builder->orWhere('pv.code LIKE %_like_', $queryPart);
			 $builder->orWhere('internalName LIKE %_like_', $queryPart);
		 }

		if ($excluded) {
			$builder->andWhere('p.id NOT IN %i[]', $excluded);
		}

		$builder->limitBy(25);

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<Product>
	 */
	public function findMainProductsInRelations(Product $attachedProduct, string $type): ICollection
	{
		$builder = $this->builder();
		$builder->joinInner( '[product_product] as pp', '[product.id] = [pp.mainProductId] AND [pp.type] = %s', $type);
		$builder->andWhere('pp.attachedProductId = %i', $attachedProduct->id);

		$builder->orderBy('pp.sort');

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<Product>
	 */
	public function findAttachedProductsInRelations(Product $mainProduct, string $type): ICollection
	{
		$builder = $this->builder();
		$builder->joinInner( '[product_product] as pp', '[product.id] = [pp.attachedProductId] AND [pp.type] = %s', $type);
		$builder->andWhere('pp.mainProductId = %i', $mainProduct->id);
		$builder->orderBy('pp.sort');

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<Product>
	 */
	public function findProductsInTreeProductRelations(Tree $tree, string $type): ICollection
	{
		$builder = $this->builder();
		$builder->joinInner( '[tree_product] as tp', '[product.id] = [tp.productId] AND [tp.type] = %s', $type);
		$builder->andWhere('tp.treeId = %i', $tree->id);
		$builder->orderBy('tp.sort');
		return $this->toCollection($builder);
	}


	public function addParameterValue(Product $product, ParameterValue $parameterValue): Result
	{
		$values = [
			'productId' => $product->id,
			'parameterId' => $parameterValue->parameter->id,
			'parameterValueId' => $parameterValue->id
		];

		return $this->connection->query('INSERT INTO product_parameter %values ON DUPLICATE KEY UPDATE %set', $values, $values);
	}



	public function removeParameterValue(Product $product, ParameterValue $parameterValue): Result
	{
		$ret = $this->connection->query('DELETE FROM product_parameter WHERE productId = %i AND parameterValueId = %i LIMIT 1', $product->id, $parameterValue->id);
		return $ret;
	}

	public function removeParameter(Product $product, Parameter $parameter): Result
	{
		$ret = $this->connection->query('DELETE FROM product_parameter WHERE productId = %i AND parameterId = %i LIMIT 1', $product->id, $parameter->id);
		return $ret;
	}

	public function removeMissingParameterValuesIds(Product $product, array $selectedParameterValuesIds = []): Result
	{
		if ($selectedParameterValuesIds) {
			$ret = $this->connection->query('DELETE FROM product_parameter WHERE productId = %i AND parameterValueId not in %i[]', $product->id, $selectedParameterValuesIds);
		} else {
			$ret = $this->connection->query('DELETE FROM product_parameter WHERE productId = %i', $product->id);
		}
		return $ret;
	}

	public function findRelationsByMainCategory(CatalogTree $catalogTree): Result
	{
		return $this->connection->query('SELECT productId from product_tree where treeId = %i and sort = 0', $catalogTree->id);
	}

	public function addToPages(Tree $page, Product $product, int $sort): Result
	{
		return $this->connection->query('INSERT INTO product_page %values', [
			'treeId' => $page->id,
			'productId' => $product->id,
			'sort' => $sort
		]);
	}


	public function removeFromPages(Tree $page, Product $product): Result
	{
		$ret = $this->connection->query('DELETE FROM product_page WHERE treeId = %i AND productId = %i LIMIT 1', $page->id, $product->id);
		//$this->getRepository()->persistAndFlush($set);
		return $ret;
	}


	public function updateToPages(Tree $page, Product $product, int $sort): Result
	{
		return $this->connection->query('UPDATE product_page SET sort=%s  WHERE treeId = %i AND productId = %i LIMIT 1',
			$sort, $page->id, $product->id);
	}

	/**
	 * @return ICollection<Product>
	 */
	public function findFilteredProducts(array $productIds) : ICollection
	{
		$builder = $this->builder()->select('p.*')->from('product', 'p')
			->andWhere('p.id in %i[]', $productIds)
			->orderBy('%raw', 'FIELD(p.id, '.implode(',', $productIds).')');
		return  $this->toCollection($builder);
	}


	public function findAllIdsForElasticSearch(?int $limit, Mutation $mutation, ?bool $skipHistorical = false): Result
	{
		$builder = $this->builder()->select('p.id')
			->from('product', 'p')

			->limitBy($limit);

		if ($skipHistorical) {
			$builder->joinLeft('[product_localization] as pl', '[p.id] = [pl.productId]');
			$builder->andWhere('([pl.public] = %i)', 1);
			$builder->andWhere('pl.mutationId = %i', $mutation->id);
		}

		return $this->connection->queryByQueryBuilder($builder);
	}

	public function findSimilarBuyIds(array $variantIds): array
	{
		$monthCount = 10;
		return $this->connection->query(
	'SELECT v.productId as productId FROM order_product AS op
			LEFT JOIN `product_variant` AS v ON (v.id = op.variantId)

			WHERE op.orderId IN (
				SELECT o.id from order_product AS op
				LEFT JOIN `order` AS o ON (o.id = op.orderId)
				WHERE o.placedAt  >= NOW() - INTERVAL %i MONTH AND op.variantId IN %i[]
			) AND op.variantId not IN %i[]
			GROUP BY v.productId', $monthCount, $variantIds, $variantIds,
		)->fetchPairs(null, 'productId');
	}

	/**
	 * @return ICollection<Product>
	 */
	public function findByExactOrder(array $ids): ICollection
	{
		$builder = $this->builder()
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}


	public function cloneProductParameters(Product $sourceProduct, Product $targetProduct): void
	{
		$tableName = 'product_parameter';
		$tmpTableName = $tableName . '_copy_tmp';
		$columns = TableHelper::getTableColumns($this->connection, $tableName);
		// clean old items
		$this->connection->query('DELETE FROM %table WHERE productId = %i', $tableName, $targetProduct->id);

		$this->connection->query('CREATE TEMPORARY TABLE %table SELECT %column[] FROM %table WHERE productId = %i', $tmpTableName, $columns, $tableName, $sourceProduct->id);
		$this->connection->query('UPDATE %table SET productId = %i', $tmpTableName, $targetProduct->id);
		$this->connection->query('INSERT INTO %table  (%column[])  SELECT %column[] FROM %table', $tableName, $columns, $columns, $tmpTableName);
		$this->connection->query('DROP TEMPORARY TABLE IF EXISTS %table', $tmpTableName);
	}


	public function findOrderedProductIds(\Nette\Security\User $user): array
	{
		if ($user->isLoggedIn()) {
			return $this->loadCache($this->createCacheKey('orderedProductIds', $user->getId()), function () use ($user) {
				$builder = $this->builder()->select('DISTINCT [p.id]')->from($this->getTableName(), 'p');
				$builder->joinLeft('[product_variant] as [pv]', '[pv.productId] = [p.id]');
				$builder->joinLeft('[order_product] as [op]', '[op.variantId] = [pv.id]');
				$builder->joinLeft('[order] as [o]', '[op.orderId] = [o.id]');
				$builder->where('[o.userId] = %i AND [o.state] NOT IN %s[]', $user->getId(), [OrderState::Draft, OrderState::Declined, OrderState::Canceled]);

				return $this->connection->queryByQueryBuilder($builder)->fetchPairs(null, 'id');
			});
		}

		return [];
	}

	public function findRelatedOrderedProducts(array $productIds, \DateTimeImmutable $orderFrom): array
	{
		if ($productIds === []) {
			return [];
		}

		return $this->loadCache($this->createCacheKey('relatedOrderedProductIds', ...func_get_args()), function () use ($productIds, $orderFrom) {
			$builder = $this->builder()->select('[o.id]')->from($this->getTableName(), 'p');
			$builder->joinLeft('[product_variant] as [pv]', '[pv.productId] = [p.id]');
			$builder->joinLeft('[order_product] as [op]', '[op.variantId] = [pv.id]');
			$builder->joinLeft('[order] as [o]', '[op.orderId] = [o.id]');
			$builder->where('[o.placedAt] >= %dt AND [o.state] NOT IN %s[] AND p.id IN %i[]', $orderFrom, [OrderState::Draft, OrderState::Declined, OrderState::Canceled], $productIds);
			$builder->orderBy('o.placedAt ' . ICollection::DESC);

			$orderIds = $this->connection->queryByQueryBuilder($builder)->fetchPairs(null, 'id');

			if($orderIds === []) {
				return [];
			}

			$builder = $this->builder()->select('DISTINCT p.id, COUNT(DISTINCT o.id) AS product_count')->from($this->getTableName(), 'p');
			$builder->joinLeft('[product_variant] as [pv]', '[pv.productId] = [p.id]');
			$builder->joinLeft('[order_product] as [op]', '[op.variantId] = [pv.id]');
			$builder->joinLeft('[order] as [o]', '[op.orderId] = [o.id]');
			$builder->where('[o.id] IN %i[] AND p.id NOT IN %i[]', $orderIds, $productIds);
			$builder->groupBy('[p.id]');
			$builder->orderBy('product_count DESC');

			return $this->connection->queryByQueryBuilder($builder)->fetchPairs('id', 'product_count');
		});
	}

	public function getPurchaseCount(Product $product, int $daysToPast = 31): int
	{
		$dateInPast = DateTime::from('now')->modify('-' . $daysToPast . 'days');

		$builder = $this->builder()
			->select('p.id')
			->addSelect('SUM(op.amount) as purchaseCount')
			->from($this->getTableName(), 'p')
			->joinLeft('[product_variant] as [pv]', '[pv.productId] = [p.id]')
			->joinLeft('[order_product] as [op]', '[op.variantId] = [pv.id]')
			->joinLeft('[order] as [o]', '[op.orderId] = [o.id]')
			->where('p.id = %i', $product->getPersistedId())
			->groupBy('p.id')
			->andWhere('[o.placedAt] >= %dt AND [o.state] NOT IN %s[]', $dateInPast, [OrderState::Draft, OrderState::Declined, OrderState::Canceled]);

		return (int) $this->connection->queryByQueryBuilder($builder)->fetch()?->purchaseCount ?: 0;
	}

	public function deactivateTagProperty(string $tag, array $productIds = []): void
	{
		$sql = <<<SQL
			UPDATE product
			SET %column = false
			WHERE id IN %i[]
SQL;

		$this->connection->query($sql, $tag,  $productIds);
	}


	/**
	 * @return ICollection<Product>
	 */
	public function getProductWithFreeTransport(int|null $offset = null, int|null $limit = null): ICollection
	{
		$builder = $this->builder()
			->select('p.*')
			->from($this->getTableName(), 'p')
			->where(
				'p.isFreeTransport = 1
				OR
				(
					p.isFreeTransportForced = 1
					AND (p.freeTransportForcedFrom < NOW() OR p.freeTransportForcedFrom IS NULL)
					AND (p.freeTransportForcedTo > NOW() OR p.freeTransportForcedTo IS NULL)
				)'
			)
			->limitBy($limit, $offset);

		return $this->toCollection($this->connection->queryByQueryBuilder($builder));
	}

	public function setPositionInMainCategory(array $productIdsWithPositions): void
	{
		$this->connection->beginTransaction();

		foreach ($productIdsWithPositions as $productId => $productPosition) {
			$this->connection->query('
				UPDATE product
				SET categoryMainPosition = %i
				WHERE
				id = %i', $productPosition, $productId);
		}

		$this->connection->commitTransaction();
	}

	public function findByParameterValue(int $parameterValueId): array
	{
		$builder = $this->builder()
			->from($this->getTableName(), 'p')
			->select('p.*')
			->joinInner('[product_parameter] as [pp]', '[pp.productId] = [p.id]')
			->where('pp.parameterValueId = %i', $parameterValueId)
			->limitBy(20);

		return $this->connection->queryByQueryBuilder($builder)->fetchPairs(null, 'id');
	}


	public function isNewProductByDateCreated(Product $product): bool
	{
		$nowMinus60Days = $this->currentDateTimeProvider->getCurrentDateTime()->modify('-60 days');
		$builder = $this->builder()->where('dateCreated > %dt', $nowMinus60Days)->andWhere('id = %i', $product->id);
		return $this->connection->queryByQueryBuilder($builder)->fetch() !== null;
	}

	/**
	 * @return ICollection<Product>
	 */
	public function findByLocalizationIds(array $ids): ICollection
	{
		$builder = $this->builder()
			->select('p.*')
			->from($this->getTableName(), 'p')
			->joinLeft('[product_localization] as [pl]', '[pl.productId] = [p.id]')
			->where('[pl.id] IN %i[]', $ids);
		return $this->toCollection($this->connection->queryByQueryBuilder($builder));
	}
}
