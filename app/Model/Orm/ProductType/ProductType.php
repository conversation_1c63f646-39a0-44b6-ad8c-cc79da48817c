<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductType;

use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $uid
 * @property string $name
 * @property string $icon {default ''}
 * @property int $sort
 */
class ProductType extends Entity
{
	public const string UID_CERTIFICATE = 'certificate';
	public const string UID_PRODUCT = 'product';
	public const string UID_CLASS = 'class';

	public const string UID_NONE = 'none';

	public const array TYPES = [
		self::UID_PRODUCT => 'produkt',
		self::UID_CLASS => 'kurz',
		self::UID_CERTIFICATE => 'certifikát',
		self::UID_NONE => 'žádný'
	];
}
