<?php
declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Country;

use App\Exceptions\LogicException;
use App\Model\Messenger\Erp\ImportConsumer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\State\State;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class CountryConsumer extends ImportConsumer
{

	public function __construct()
	{
	}

	protected function setup(): void
	{
		// TODO: Implement setup() method.
	}

	public function __invoke(CountryMessage $message): void
	{
		$this->checkMessageAndImport($message);
	}
	protected function doRemove(): void
	{
		throw new LogicException('Remove message is not implemented yet.');
	}
	protected function doImport(): void
	{
		$createNew = true;
		if (($state = $this->orm->state->getByExtId($this->importCacheOdoo->extId)) !== null) {
			$createNew = false;
		}

		if (($stateByCode = $this->orm->state->getBy(['code' => $this->importCacheOdoo->data->code])) !== null && $createNew) {
			$createNew = false;
			$state = $stateByCode;
		}

		if ($createNew) {
			$state = new State();
			$this->orm->state->attach($state);
			$state->code = $this->importCacheOdoo->data->code;
		}

		$state->extId = $this->importCacheOdoo->extId;
		$state->name = $this->importCacheOdoo->data->name->{Mutation::DEFAULT_ISO_CODE};
		$state->public = 1;

		$this->orm->state->persistAndFlush($state);
	}

	protected function save(): void
	{
		// Do some stuff before importCacheChange persists
		parent::save();
	}

}
