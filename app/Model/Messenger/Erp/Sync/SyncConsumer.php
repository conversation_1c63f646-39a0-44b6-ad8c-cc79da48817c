<?php
declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Sync;

use App\Model\Odoo\Events\Event\SyncError;
use App\Model\Odoo\Events\Event\SyncSkipped;
use App\Model\Odoo\Events\Event\SyncSucceeded;
use App\Model\Odoo\OdooClient;
use App\Model\Odoo\OdooEndpoint;
use App\Model\Odoo\OdooException;
use App\Model\Orm\ExportCache\ExportCache;
use App\Model\Orm\ExportCache\ExportCacheStatus;
use App\Model\Orm\Orm;
use App\Model\Orm\Synchronizable;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use Tracy\Debugger;
use Tracy\ILogger;

#[AsMessageHandler]
final readonly class SyncConsumer
{

	public function __construct(
		private OdooClient $odooClient,
		private Orm $orm,
		private EventDispatcherInterface $eventDispatcher,
	)
	{
	}

	protected function setup(): void
	{
		$this->orm->reconnect();
		//$this->orm->clear();
	}
	public function __invoke(SyncMessage $message): void
	{
		$this->setup();

		$endpoint = $message->getEndpoint();
		$this->odooClient->endpoint($endpoint);

		if (!$endpoint->isUpToDate($this->orm, $message->getCreatedAt())) {
			$debug = $endpoint->isUpToDateDebug($this->orm, $message->getCreatedAt());
			$this->logExport($endpoint, 'skipped due to outdated data', $debug);
			return;
		}

		if (!$endpoint->isAllowedSync($this->orm)) {
			$this->logExport($endpoint, 'sync is not allowed for this order');
			return;
		}

		$exportCache = new ExportCache();
		$exportCache->endpoint = $this->odooClient->getEndpointUri();
		$exportCache->status = ExportCacheStatus::Dispatched;
		$exportCache->data = $endpoint->jsonSerialize();
		$exportCache->model = $endpoint->getModel();
		$exportCache->createdTime = new DateTimeImmutable();
		$exportCache->dispatchTime = new DateTimeImmutable();
		try {

			$response = $this->odooClient->call();
			$this->eventDispatcher->dispatch(new SyncSucceeded($endpoint, $response));

			if (
				($entity = $endpoint->getOdooEntity()->createOrmEntity($this->orm)) !== null &&
				$entity instanceof Synchronizable
			) {
				$entity->setExternalId((string) $response['data']['id']);
				$this->orm->persist($entity);
			} elseif (
				$response['result'] === false
			) {
				$response['error']['message'] = 'API call result is false.';
				throw new OdooException($response);
			}

			$exportCache->response = $response;
			$exportCache->status = ExportCacheStatus::Success;
			$exportCache->exportTime = new DateTimeImmutable();
			$exportCache->message = 'ok';
			$this->orm->persistAndFlush($exportCache);
		} catch (\Throwable $e) {
			if ($e instanceof OdooException) {
				$exportCache->response = $e->getResponseArray();
			}
			$exportCache->message = $e->getMessage();
			$exportCache->status = ExportCacheStatus::Error;
			$this->orm->persistAndFlush($exportCache);

			Debugger::log($e, ILogger::EXCEPTION);

			$this->eventDispatcher->dispatch(new SyncError($endpoint, $exportCache->response ?? [], $e));
		}
	}

	private function logExport(OdooEndpoint $endpoint, string $message, array $response = []): void
	{
		$exportCache = new ExportCache();
		$exportCache->endpoint = $this->odooClient->getEndpointUri();
		$exportCache->data = $endpoint->jsonSerialize();
		$exportCache->model = $endpoint->getModel();
		$exportCache->createdTime = new DateTimeImmutable();
		$exportCache->response = $response;
		$exportCache->status = ExportCacheStatus::Skipped;
		$exportCache->message = $message;
		$this->orm->persistAndFlush($exportCache);
		$this->eventDispatcher->dispatch(new SyncSkipped($endpoint, $response, $message));
	}

}
