{default $class = false}
{default $shouldHideFilters = false}

<div n:if="count($filter->boxes ?? [])" n:class="b-filter, $class">
	<div class="b-filter__box">
		<p class="b-filter__title h4">
			{_"filter_title"}
		</p>

		{snippetArea filterSelectedWrapper}
			{include '../../../Presenters/Catalog/templates/part/selectedFilters.latte', class: 'b-filter__selected b-catalog__selected'}
			{include '../../../Presenters/Catalog/templates/part/sort.latte', class: 'b-filter__sort b-catalog__sort', showingLang: isset($object->hasCourses) && $object->hasCourses ? 'showing_courses' : 'showing', sortOptions: $sortingOptions}
		{/snippetArea}

		<div class="b-filter__helper overlay-pseudo" data-controller="toggle-class etarget">
			<p class="b-filter__btn-wrap u-mb-0">
				<button type="button" class="b-filter__btn btn" data-action="toggle-class#toggle">
					<span class="btn__text">
						<span class="btn__inner">
							{('filter')|icon, 'btn__icon'}
							{_"btn_filter_products"}
						</span>
					</span>
				</button>
			</p>
			{if !$shouldHideFilters}
				<div class="b-filter__filter b-catalog__filter">
					{snippet filterArea}
						{include '../../../Presenters/Catalog/templates/part/filter.latte', class=>false}
					{/snippet}
				</div>
			{/if}
		</div>
	</div>
</div>
