{default $class = false}
{default $cfPerson = $pages->contact->cf->contactPerson ?? false}
{default $cfContacts = $pages->contact->cf->contacts ?? false}

<p n:if="$cfPerson || $cfContacts" n:class="b-person-header, $class">
	{php $name = $cfPerson->name ?? false}
	{php $img = isset($cfPerson->image) ? $cfPerson->image->getEntity() ?? false : false}
	{include $templates.'/part/core/person.latte', class: 'b-person-header__person', img: $img, name: $name}
	<span>
		<span class="b-person-header__text">
			{_"header_contact_text"}
		</span>
		<span class="b-person-header__call" n:if="$cfContacts->phone ?? null">
			<a href="tel:+420{$cfContacts->phone|replace:' ',''}" class="b-person-header__link">{$cfContacts->phone}</a> ({$cfContacts->phone_hours})
		</span>
	</span>
</p>
