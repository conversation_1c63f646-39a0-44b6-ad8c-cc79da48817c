{default $class = 'u-mb-md u-mb-2xl@md'}
{varType App\Model\Orm\Product\Product $product}
{var $selectedCurrency = App\Model\Currency\CurrencyHelper::getCurrency()}

{* {var $showMoreCount = 3}
{var $showed = 0}

{var array $relatedProducts = []}
{foreach $product->relatedProducts as $item}
	{var $relatedProductDto = $productDtoProviderCallback($item)}
	{continueIf in_array($relatedProductDto->productAvailabilityType, [App\Model\Orm\ProductVariant\Availability\CustomProductAvailability::TYPE_NOT_FOR_SALE, App\Model\Orm\ProductVariant\Availability\CustomProductAvailability::TYPE_OUT_OF_STOCK])}
	{php $relatedProducts[] = $item}
{/foreach}

{var $relatedCount = count($relatedProducts)}

<div n:if="$relatedCount > 0" n:class="c-variants, $class">
	<hr class="u-d-n u-d-b@md">
	<h2 class="c-variants__title">
		{_"variants_title"}
	</h2>
	<ul class="c-variants__list">
		<li class="c-variants__item{if $showed >= $showMoreCount} u-d-n{/if}" n:foreach="$relatedProducts as $item">
			{varType App\Model\Orm\Product\Product $item}
			{var $relatedProductDto = $productDtoProviderCallback($item)}
			{var $itemVariant = $item->firstVariant}
			{var $availabilityShortText = $relatedProductDto->productAvailabilityShortText}
			{var $priceItemVat = $relatedProductDto->priceVat}

			{if $showed <= $showMoreCount}
				{php $showed++}
			{/if}
			<a href="{plink $item}" class="c-variants__img img img--book">
				{if $itemVariant->firstImage}
					<img src="{$itemVariant->firstImage->getSize('sm')->src}" loading="lazy" alt="{$item->name}">
				{else}
					<img src="/static/img/illust/noimg.svg" alt="{$item->name}" loading="lazy">
				{/if}
			</a>
			<a href="{plink $item}" class="c-variants__name">{$item->name}</a>
			<span class="c-variants__type type item-icon">
				{($item->getProductTypeIconName())|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{$item->getProductTypeName()}
				</span>
			</span>
			<span class="c-variants__price">
				{var $priceInfo = $item->getPriceInfo($mutation, $priceLevel, $state)}
				<s n:if="$priceInfo->getOriginalPrice() ?? false" class="u-c-red">{$priceInfo->getOriginalPrice()|money}</s>
				{$priceItemVat|money}
			</span>
			{include $templates.'/part/core/availability.latte', class: 'c-variants__availability', showTooltip: false, variant: $itemVariant}
		</li>
	</ul>
	<p class="c-variants__more" n:if="$relatedCount > $showMoreCount">
		<button type="button" class="item-icon item-icon--arrow as-link" data-controller="toggle-class" aria-expanded="false" data-action="toggle-class#toggle" data-toggle-class="u-d-n" data-toggle-content=".c-variants__item.u-d-n">
			<span class="item-icon__text">
				{capture $countMoreLang}{($relatedCount-$showed+1)|plural: "variants_more_1", "variants_more_2", "variants_more_3" }{/capture}
				{_$countMoreLang->__toString()|replace:'%count',(string)($relatedCount-$showed+1)}
			</span>
			{('angle-down')|icon, 'item-icon__icon'}
		</button>
	</p>
</div> *}

<section n:class="c-variants, $class">
	<div class="u-maw-11-12 u-mx-auto">
		<h2 class="c-variants__title h3">
			{_"title_variants"}
		</h2>
		<div class="c-variants__inner">
			{include $templates.'/part/box/help.latte', class: 'c-variants__contact b-help--big'}
			<div class="c-variants__carousel embla" data-controller="embla">
				<div class="embla__viewport" data-embla-target="viewport">
					<div class="c-variants__grid grid grid--scroll grid--x-0 grid--y-0 embla__container">
						<div class="c-variants__cell grid__cell">
							{include $templates.'/part/box/variant.latte', class: 'is-active', product: $product}
						</div>
						{foreach $product->products as $item}
							<div class="c-variants__cell grid__cell">
								{include $templates.'/part/box/variant.latte', class: '', product: $item}
							</div>
						{/foreach}
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
