{varType App\Model\Orm\Order\Class\ClassItem $classItem}

{default $class = 'tw-mb-[2.4rem] md:tw-mb-[3.2rem]'}
{default $title = false}
{default $emptyMsg = false}
{default $items = []}

<div n:if="count($items) || $emptyMsg" n:class="$class">
	<h2 n:if="$title" class="h3 tw-mb-[1.2rem]">{$title}</h2>
	{if count($items)}
		<table class="c-table tw-grid-cols-[auto_1fr] md:tw-grid-cols-[max-content_auto_auto_auto] tw-text-[1.3rem] md:tw-text-[1.5rem]">
			<thead>
				<tr class="u-c-help tw-text-[1.3rem] tw-gap-[0.8rem] md:tw-gap-[3rem] xxxl:tw-gap-[6rem]">
					<th>{_"course_form"}</th>
					<th>{_"course_name"}</th>
					<th>{_"course_date"}</th>
					<th>{_"course_length_place"}</th>
				</tr>
			</thead>
			<tbody>
			{foreach $items as $classItem}
				<tr class="tw-gap-[0.8rem] md:tw-gap-[3rem] xxxl:tw-gap-[6rem] tw-p-[1.2rem_1.6rem] md:tw-p-[2rem]">
					<td>
						<span n:if="!empty($classItem->getEventClassType())" class="flag flag--sm flag--purple-light">
							{$classItem->getEventClassType()}
						</span>
					</td>
					<td class="max-md:tw-row-start-1 max-md:tw-col-start-1 max-md:tw-col-end-3">
						<a n:tag-if="$classItem->product !== null" href="{plink $classItem->product}" class="tw-font-semibold">
							{$classItem->getProductName()}
						</a>
					</td>
					<td>
						{if $classItem->getEventDate() !== null && $classItem->getEventDate() !== ''}
							{$classItem->getEventDate()}
							<span n:if="$classItem->getEventTime() !== null && $classItem->getEventTime() !== ''" class="tw-text-placeholder tw-text-[1.3rem] max-md:tw-ml-[0.4rem] md:tw-block">
								{$classItem->getEventTime()}
							</span>
						{/if}
					</td>
					<td class="max-md:tw-col-start-1 max-md:tw-col-end-3">
						<span n:if="$classItem->getDuration() !== null && $classItem->getDuration() !== ''">
							{$classItem->getDuration()|totalDuration}
						</span>
						<span n:if="$classItem->getPlace() !== null && $classItem->getPlace() !== ''" class="tw-text-placeholder tw-text-[1.3rem] max-md:tw-ml-[0.4rem] md:tw-block">
							{$classItem->getPlace()}{* - <a href="#" class="">{_"map_details"}</a>*}
						</span>
					</td>
				</tr>
			{/foreach}
			</tbody>
		</table>
	{else}
		<p class="message message--md u-mb-0">
			<span class="message__emoji">👉</span>
			<span class="message__content">
				{$emptyMsg|noescape}
			</span>
		</p>
	{/if}
</div>
