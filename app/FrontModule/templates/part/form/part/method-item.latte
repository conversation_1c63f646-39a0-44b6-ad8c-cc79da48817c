{default $class = false}
{default $name = false}
{default $id = false}
{default $title = false}
{default $deliveryDate = false}
{default $price = 0}
{default $imgs = []}
{default $tooltipContent = false}
{default $isSelected = false}
{default $isGift = false}
{default $isAllowed = true}
{default $isRecommended = false}
{default $freeFrom = false}
{default $item = false}
{default $isGroup = false}
{default $isDisabled = false}
{default $control = null}

{php $supportsPickup = $item instanceof App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration && $item->getDeliveryMethod()->getUniqueIdentifier() === 'ZasilkovnaPickup'}
<li id="{strtolower(str_replace(' ', '', $title))}" n:class="f-method__item, $class, $supportsPickup ? f-method__item--pickup, $isGroup ? f-method__item--group" >
	<label class="f-method__label inp-item inp-item--radio" >
		<input 
			n:name="$name:$id" 
			class="f-method__inp inp-item__inp" 
			{if !$isAllowed || $isDisabled} 
				disabled
			{/if}
			{if $supportsPickup} 
				onclick="Packeta.Widget.pick('2e34304f2b24434b', showSelectedPickupPoint, packetaOptions)"
			{/if}
			data-controller="autosubmit" data-action="autosubmit#submitForm"
		>
		<span class="f-method__inner inp-item__text">
			{define #imgs}
				{default $showEmpty = false}
				{default $size = 46}
				{default $contain = false}

				<span n:ifcontent class="f-method__img">
					{if count($imgs)}
						{foreach $imgs as $img}
							<img class="img, $contain ? img--contain" src="{$img->getSize('xs')->src}" alt="{$img->getAlt($mutation)}" loading="lazy" {*$sizes|noescape*} width="{$size}" height="{$size}">
						{/foreach}
					{elseif $showEmpty}
						<img class="img, $contain ? img--contain" src="/static/img/illust/noimg.svg" alt="" loading="lazy" {*$sizes|noescape*} width="{$size}" height="{$size}">
					{/if}
				</span>
			{/define}

			{if $name == 'deliveryMethod'}
				{include #imgs, showEmpty: true}
			{/if}

			<span class="f-method__content">
				<b class="f-method__title">
					{$title}
					{if $tooltipContent}
						{embed '../../core/tooltip.latte', class: 'f-method__tooltip tooltip--gray', placement: 'right-start'}
							{block btn}
								{('info-outline')|icon, 'tooltip__icon'}
							{/block}
							{block content}
								{$tooltipContent}
							{/block}
						{/embed}
					{/if}

					{if $name == 'paymentMethod'}
						{include #imgs, size: 25, contain: true}
					{/if}

					<span n:if="$isRecommended" class="f-method__flag flag flag--green-light">{_"recommend"}</span>
				</b>
				<span n:if="$item->desc ?? false" class="f-method__desc">{$item->desc}</span>
			</span>
			<span n:if="$name == 'deliveryMethod' && $isAllowed && !$isDisabled" class="f-method__delivery u-c-green">
				{include $templates . '/part/box/deliveryInfo.latte', deliveryDate: $deliveryDate,}
			</span>
			<span n:if="$price" class="f-method__price">
				{if $isAllowed}
					{if $price->isZero()}
						{_"free"}
					{else}
						{$price|money}
					{/if}
				{else}
					{_'cart_method_is_not_allowed'}
				{/if}
			</span>
			<span n:ifcontent class="f-method__point">
				{* Výběr odběrného místa *}
				<span n:if="$supportsPickup" class="b-point">
					{if isset($pickupPoint)}
						<span class="grid grid--x-xs grid--y-xs grid--nowrap">
							<span class="grid__cell size--6-12">
								{$pickupPoint->place}
								<span class="b-point__text">
									{$pickupPoint->name}
								</span>
								<span class="b-point__link as-link u-d-b">
									{_"delivery_change_store"}
								</span>
							</span>
							<span n:if="count($pickupPoint->openingHours ?? [])" class="grid__cell size--6-12">
								{_"contact_store_hours"}
								<span class="b-point__text">
									{foreach $pickupPoint->openingHours as $openingHour}
										{_$openingHour->getDay()}: {$openingHour->getFromTo()}{sep}, {/sep} <br>
									{/foreach}
								</span>
							</span>
						</span>
					{else}
						<span class="b-point__link as-link u-d-b">
							{_'delivery_change_store'}
						</span>
					{/if}
				</span>
			</span>
			<span n:if="$freeFrom" class="f-method__free-from">
				{_"cart_free_delivery_from"} {$freeFrom|money}
			</span>
		</span>
	</label>

	{block otherOptions}{/block}

	{* Pozn.: Nesmí být <button> *}
	{* {if $control !== null && $control instanceof App\FrontModule\Components\CartDelivery\CartDelivery}
		{var $changeUrl = $item instanceof App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration ? $control->link('changeDelivery!') : $control->link('changePayment!')}
		<a href="{$changeUrl}" n:if="$isSelected" class="f-method__change item-icon" data-naja data-naja-history="off" data-naja-loader="body">
			<span class="item-icon__text">
				{_"btn_change_".$name}
			</span>
			{('angle-down')|icon, 'item-icon__icon'}
		</a>
	{/if} *}


</li>
