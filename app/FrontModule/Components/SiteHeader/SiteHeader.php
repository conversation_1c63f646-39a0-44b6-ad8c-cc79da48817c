<?php declare(strict_types = 1);

namespace App\FrontModule\Components\SiteHeader;

use App\FrontModule\Components\Cart\Cart;
use App\FrontModule\Components\Cart\CartFactory;
use App\FrontModule\Components\CurrencyDetector\CurrencyDetector;
use App\FrontModule\Components\CurrencyDetector\CurrencyDetectorFactory;
use App\FrontModule\Components\CurrencyToggle\CurrencyToggle;
use App\FrontModule\Components\CurrencyToggle\CurrencyToggleFactory;
use App\FrontModule\Components\Menu\Menu;
use App\FrontModule\Components\Menu\MenuFactory;
use App\FrontModule\Components\ServiceMenu\ServiceMenu;
use App\FrontModule\Components\ServiceMenu\ServiceMenuFactory;
use App\FrontModule\Components\SignInForm\SignInForm;
use App\FrontModule\Components\SignInForm\SignInFormFactory;
use App\FrontModule\Components\Suggest\Suggest;
use App\FrontModule\Components\Suggest\SuggestFactory;
use App\FrontModule\Components\UserMenu\UserMenu;
use App\FrontModule\Components\UserMenu\UserMenuFactory;
use App\FrontModule\Presenters\Homepage\HomepagePresenter;
use App\Model\Orm\Routable;
use App\Model\Orm\User\UserProvider;
use App\Model\Setup;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\PostType\SystemMessage\Model\Orm\SystemMessageRepository;
use Nette\Application\UI\Control;
use Nette\Http\Request;

class SiteHeader extends Control
{

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly Setup $setup,
		private readonly TranslatorDB $translator,
		private readonly SystemMessageRepository $systemMessageRepository,
		private readonly Request $httpRequest,
		private readonly SuggestFactory $suggestFactory,
		private readonly CurrencyDetectorFactory $currencyDetectorFactory,
		private readonly ServiceMenuFactory $serviceMenuFactory,
		private readonly CurrencyToggleFactory $currencyToggleFactory,
		private readonly MenuFactory $menuFactory,
		private readonly UserMenuFactory $userMenuFactory,
		private readonly SignInFormFactory $signInFormFactory,
		private readonly CartFactory $cartFactory,
		private readonly UserProvider $userProvider,
	)
	{
	}

	public function render(): void
	{
		$this->showSystemMessages();

		$this->template->setTranslator($this->translator);
		$this->template->mutation = $this->setup->mutation;
		$this->template->object = $this->object;
		$this->template->pages = $this->setup->mutation->pages;
		$this->template->isHomepage = $this->presenter instanceof HomepagePresenter;
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->userEntity = $this->setup->userEntity;

		$this->template->render(__DIR__ . '/siteHeader.latte');
	}

	public function showSystemMessages(): void
	{
		$systemMessage = null;
		foreach ($this->systemMessageRepository->findPublicInMutation($this->setup->mutation) as $msg) {
			$msgCookieId = 'msg-' . $msg->id . '-' . $msg->editedTime?->getTimestamp();
			if ($this->httpRequest->getCookie($msgCookieId)) {
				continue;
			}
			$systemMessage = $msg;
			break;
		}

		$this->template->systemMessage = $systemMessage;
	}

	protected function createComponentCurrencyDetector(): CurrencyDetector
	{
		return $this->currencyDetectorFactory->create($this->setup->userEntity);
	}

	protected function createComponentSuggest(): Suggest
	{
		return $this->suggestFactory->create(
			$this->setup
		);
	}

	protected function createComponentServiceMenu(): ServiceMenu
	{
		return $this->serviceMenuFactory->create($this->object);
	}

	public function createComponentCurrencyToggle(): CurrencyToggle
	{
		return $this->currencyToggleFactory->create(
			$this->setup->mutation
		);
	}

	protected function createComponentMenu(): Menu
	{
		return $this->menuFactory->create(
			$this->object,
			$this->setup,
		);
	}

	protected function createComponentUserMenu(): UserMenu
	{
		return $this->userMenuFactory->create($this->object, $this->setup->userEntity);
	}

	protected function createComponentSignInFormHeader(): SignInForm
	{
		return $this->signInFormFactory->create($this->setup->mutation, $this->object);
	}
	public function createComponentCart(): Cart
	{
		return $this->cartFactory->create($this->userProvider, $this->setup->state, $this->setup->priceLevel); //->setShowFlashes();
	}

}
