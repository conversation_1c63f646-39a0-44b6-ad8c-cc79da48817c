{varType App\Model\Orm\Product\Product $productEntity}
{varType App\Model\Orm\ClassEvent\ClassEvent $closestEvent}
{varType App\Model\Orm\ProductVariant\ProductVariant $variant}
{varType App\Model\DTO\Product\ProductDto $productDto}

{snippet productBox}
	{default $productEntity = $productEntity ?? $product}
	{default $class = false}
	{default $titleTag = 'h3'}
	{default $lazyLoading = true}
	{default $dataAttrs = []}
	{default $closestEvent = $productEntity->classEventPublicClosest ?? null}
	{default $lector = $closestEvent?->lectors->toCollection()->fetch() ?? null}
	{default $type = $productEntity->productType?->uid}
	{default $isCourse = App\Model\Orm\ProductType\ProductType::UID_CLASS === $type}
	{default $titleTag = 'h3'}
	{default $hasVariants = $colorsPickerParameterLists !== []}
	{var App\Model\Orm\ProductLocalization\ProductLocalization $productLocalization = $productEntity->getLocalization($mutation)}
	{default $showHighlight = false} {* TODO BE: zobrazeni barevneho stitku nahore *}

	<article n:class="b-product, $class, link-mask" {foreach $dataAttrs ?? [] as $dataAttrName => $dataAttr}data-{$dataAttrName}="{$dataAttr}" {/foreach} {if $hasVariants}data-controller="product"{/if}>
		{* tw-bg-status-valid-light, tw-bg-primary-150, tw-bg-violet-200, tw-bg-status-alert-light *}
		<p n:if="$ratingLocalization ?? false" class="tw-p-[0.8rem] tw-pb-0 tw-mb-[2.4rem]">
			{var $color = $ratingLocalization?->cf->settings?->color ?? null}
			{var $colorClass = match($color) {
				'blue' => 'tw-bg-status-valid-light',
				'green' => 'tw-bg-primary-150',
				'yellow' => 'tw-bg-violet-200',
				'red' => 'tw-bg-status-alert-light',
				default => 'tw-bg-status-valid',
			}}
			{var $image = $ratingLocalization?->cf->settings?->image ?? null}
			{var $imageEntity = $image?->getEntity() ?? null}
			<b class="tw-flex tw-items-center tw-gap-[1.2rem] tw-justify-center tw-rounded-md tw-p-[0.5rem_1rem] {$colorClass} tw-text-[1.5rem]">
				<img n:if="$imageEntity" src="{$imageEntity->getSize('md')->src}" alt="{$imageEntity->alt ?? ''}" loading="lazy" class="tw-w-[6rem] tw-h-[6rem]">
				{$ratingLocalization?->name}
			</b>
		</p>

		<div n:class="'tw-p-[2.8rem_1.6rem] md:tw-p-[4.4rem] tw-flex-1 tw-flex tw-flex-col', $showHighlight ? 'tw-pt-[0] md:tw-pt-0'">
			<div n:class="b-product__img-wrap, $hasVariants ? link-mask__unmask">
				<p class="b-product__img u-mb-0">
					<a href="/{$productDto->alias}">
						{if $productDto->firstImageObjectMd !== null}
							<img class="img img--4-3" src="{$productDto->firstImageObjectMd->src}" alt="{$productDto->firstImageAlt}"{if $lazyLoading} loading="lazy"{else} fetchpriority="high"{/if} width="321" height="241">
						{else}
							<img class="img img--4-3" src="/static/img/illust/noimg.svg" alt=""{if $lazyLoading} loading="lazy"{else} fetchpriority="high"{/if} width="321" height="241">
						{/if}

						<img n:if="$hasVariants" class="b-product__img--hover img img--4-3" src="/static/img/illust/noimg.svg" alt="" loading="lazy" {if $hasVariants}data-product-target="hoverImg"{/if}>
					</a>
				</p>

				{include $templates.'/part/core/flags.latte', class=>'b-product__flags', hasLastMinute: $hasLastMinute}
				{if $isCourse}{include $templates.'/part/core/type.latte', class: 'b-product__type flag--type'}{/if}
				{include $templates.'/part/core/badge.latte', class: 'b-product__badge', badge: $variant->cf->badge ?? false}

				{* Varianty *}
				<p n:if="$hasVariants" class="b-product__variants tw-mb-0 tw-absolute tw-bottom-[-2rem] tw-left-0 tw-right-0 embla tw-overflow-visible" data-controller="embla">
					<span class="embla__viewport tw-block tw-overflow-hidden" data-embla-target="viewport">
						<span class="tw-flex tw-gap-[0.4rem] embla__container">
							{foreach $colorsPickerParameterLists as $i=>$item}
								{var $libraryImage = $item['image']}
								{var $hasImage = $libraryImage !== null}
								{var $image = $hasImage ? $libraryImage->getSize('md') : null}
								{php $btnClass = "tw-relative tw-no-underline tw-transition-[border-color,color] tw-duration-300 tw-flex-[0_0_auto] tw-bg-white tw-border-tile tw-border-solid tw-border-[0.1rem] tw-rounded-md before:tw-content-[''] before:tw-absolute before:tw-inset-[-0.1rem] before:tw-rounded-md before:tw-border-[0.2rem] before:tw-border-solid before:tw-border-primary before:tw-transition-[opacity] before:tw-duration-300 before:tw-opacity-0 hover:tw-border-primary [&.is-active]:before:tw-opacity-[1]"}
								<a href="{$item['href']}" n:class="$btnClass, $iterator->isFirst() ? 'tw-ml-auto is-active', $iterator->isLast() ? 'tw-mr-auto'"
									{if $image ?? false}
										data-hover-src="{$image->src}"
									{/if}
									data-action="mouseenter->product#setImage"
								>
									{if $hasImage}
										<img class="img img--4-3 tw-w-[6.4rem] tw-rounded-md" src="{$image->src}" alt="{$item['parameterValue']->value}" loading="lazy">
									{else}
										<b class="tw-flex tw-items-center tw-justify-center tw-h-full tw-min-h-[4.4rem] tw-p-[0.6rem_0.8rem]">{$item['parameterValue']->value}</b>
									{/if}
								</a>
							{/foreach}
						</span>
					</span>
					<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
						{('arrow-left')|icon}
						<span class="u-vhide">{_btn_prev}</span>
					</button>
					<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
						{('arrow-right')|icon}
						<span class="u-vhide">{_btn_next}</span>
					</button>
				</p>

				{php $tag = $productEntity->getTag(App\PostType\Tag\Model\TagType::videoReview)}
				{ifset $tag}
					{php $tagLocalization = $tag->getLocalization($mutation)}
					<span class="b-product__videoreview flag flag--blue-light tw-uppercase">
						{('play')|icon, 'flag__icon'}
						{$tagLocalization->name}
					</span>
				{/ifset}

			</div>

			<div class="u-mb-last-0 tw-mb-[1.6rem]">
				<p n:if="!$isCourse" class="b-product__category-rating">
					{if !isset($shouldHideCategory) || !$shouldHideCategory}
						<a href="{$productEntity->mainCategory?->alias}" class="b-product__category link-mask__unmask">
							{$productEntity->mainCategory?->name}
						</a>
					{/if}
					{* {include $templates.'/part/core/stars.latte', class=>'b-product__rating', stars: 80} *}
					{cache cacheKey('reviewInfo-percent', $productEntity), expire: '24 hour', tags: $productEntity->getTemplateCacheTags()}
						{if $productEntity->reviewInfo['percent']}
							{include $templates.'/part/core/stars.latte', class=>'b-product__rating', stars: $productEntity->reviewInfo['percent'] * 5 / 100}
						{/if}
					{/cache}
				</p>

				<h3 n:tag="$titleTag" class="b-product__title h4">
					<a href="/{$productDto->alias}" class="b-product__link link-mask__link prefetch-click" data-id="{$productDto->productId}" data-list-id="{$listId ?? ''}" data-list-name="{$listName ?? ''}">
						{$parametersToTemplate['name'] ?? $productDto->nameAnchor}
					</a>
				</h3>

				{if $isCourse}
					<div class="b-product__info u-mb-last-0 tw-mb-[0.8rem]">
						{* Délka školení *}
						<p>{_"course_length"}: <b>{$productEntity->getDuration()|totalDuration}</b></p>

						{if $closestEvent !== null}
							{* Města *}
							<p class="b-product__dates">
								{php $daysToGo = $closestEvent->daysToGo}
								{capture $pluralDays}{$daysToGo|plural: "day_plural_1", "day_plural_2", "day_plural_3" }{/capture}
								<span>
									{_"course_closest_dates"}: {if !$productEntity->isOnlineCourse()}<b>za {$daysToGo} {_$pluralDays->__toString()}</b>{/if}
								</span>

								{if $closestEvent->city !== ''}
									<span class="b-product__city u-c-help">({$closestEvent->city})</span>
								{else}
									{php $courseType = $productEntity->getCourseTypeParameterValue()}
									<span n:if="$courseType !== null" class="b-product__city u-c-help">({$courseType->value})</span>
								{/if}

							</p>

							{* Volná místa *}
							{if !$productEntity->isOnlineCourseWithoutEvent()}
								{php $freeSpots = $closestEvent->capacity-$closestEvent->capacityUsed}
								{capture $pluralLang}{$freeSpots|plural: "free_spot_1", "free_spot_2", "free_spot_3" }{/capture}
								<p>{_"course_spots"}: <span n:class="($hasLastMinute ?? false) ? u-c-red : u-c-green">{$freeSpots} {_$pluralLang->__toString()}</span></p>
							{/if}

						{/if}
					</div>
				{/if}

				{* {if false} *}
					<div n:if="$parametersToTemplate['annotation'] ?? $productEntity->annotation ?? false" class="b-product__desc u-mb-last-0 tw-mb-[0.4rem]">
						{$parametersToTemplate['annotation'] ?? $productEntity->annotation|noescape|replaceRE:'/\s+/',' '|trim|truncateSafe:200}
					</div>
				{* {/if} *}

				{* TODO BE: Hmotnost *}
				{* <p class="tw-text-help tw-mb-[0.4rem]">Hmostnost: 2 095 g</p> *}

				{if !$isCourse && $productLocalization->getPackageContents() !== []}
				 	<p class="tw-text-help tw-mb-[0.4rem] tw-line-clamp-2">
						{_"product_contains"}:
						{foreach $productLocalization->getPackageContents() as $item}
							<span n:if="$item['amount'] > 1">
								{$item['amount']}x
							</span>
							{$item['name']}{sep}, {/sep}
						{/foreach}
					</p>
				{/if}
			</div>

			<div class="b-product__bottom">
				{if $isCourse}
					{* 	Úhrada od UP *}
					<p n:if="$productLocalization->hasRequalificationPossibility($state)" class="tw-rounded-md tw-mb-[1.6rem] tw-p-[0.8rem] tw-flex tw-items-center tw-gap-[1rem] tw-text-[1.3rem] tw-bg-alert-light">
						<span class="tw-rounded-sm tw-p-[0.8rem] tw-bg-gradient-to-r tw-from-yellow-600 tw-to-[#FFE55A]">
							{('cz')|icon, 'tw-w-[2.5rem]'}
						</span>
						<span>
							{*Lze získat <b>100%</b> úhradu od Úřadu práce!*}
							{translate('product_box_requalification_text_' . $productLocalization->requalificationPossibility, ['%price%' => App\Infrastructure\Latte\Filters::formatMoney($productEntity->requalificationPriceVat($mutation, $state))])|noescape}
						</span>
						{embed $templates.'/part/core/tooltip.latte', class: 'link-mask__unmask', btnClass=>'as-link', placement: 'right'}
							{block btn}
								{('info-outline')|icon}
							{/block}
							{block content}
								{_'product_box_requalification_tooltip_' . $productLocalization->requalificationPossibility}
							{/block}
						{/embed}
					</p>
				{else}
					{include $templates.'/part/core/availability.latte', class: 'b-product__availability', variant: $variant}
				{/if}

				{* TODO BE: marketingové sdělení *}
				{* <p class="tw-bg-status-valid-light tw-border-[0.1rem] tw-border-dashed tw-border-status-valid tw-flex tw-items-center tw-rounded-md tw-p-[0.4rem_1rem] tw-text-center tw-mb-[1.6rem]">
					<span class="tw-flex-1 tw-text-[1.3rem]">Protiúčet - bonus 20 %</span>
					<a href="#" class="tw-flex-[0_1_5rem] u-c-help tw-text-[1.1rem] link-mask__unmask" target="_blank" rel="noopener noreferrer">{_"how_it_works"}</a>
				</p> *}

				{include $templates.'/part/core/countdown.latte', class: false}

				<div class="b-product__price-add">
					{include $templates.'/part/core/price.latte', class: 'b-product__price u-mb-0', showVatText: false, product: $variant}
					{* Do košíku - nedostupné, o výkupu, porovnání *}
					{if $productDto->productDirectBuyAllowed && !$presenter->configService->isTagged('prod')}
						{include $templates.'/part/form/add.latte', variant: $variant, showInput: false, btnClass: 'link-mask__unmask btn--bd', listId: $listId, listName: $listName}
					{else}
						<p class="tw-mb-0 tw-ml-auto">
							<a href="{plink $productEntity}" n:class="b-product__btn, btn, !$isCourse ? btn--bd, link-mask__unmask">
								<span class="btn__text">
									{var $btnText = $parametersToTemplate['btnText'] ?? null}
									{if $btnText}
										{$btnText}
									{else}
										{if $isCourse}
											{_"btn_detail_course"}
										{else}
											{_"btn_detail"}
										{/if}
									{/if}
								</span>
							</a>
						</p>
					{/if}
					{if !$variant->product->isCourse()}
						{control addToCompareComponent}
					{/if}
				</div>
			</div>
		</div>
	</article>
{/snippet}
