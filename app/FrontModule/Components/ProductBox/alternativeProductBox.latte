{default $class = false}

{default $hasVariants = false}
{default $packageContentsKeys = []}
{varType App\Model\DTO\Product\ProductDto $productDto}
{varType App\Model\Orm\Product\Product $productEntity}
{varType App\Model\Orm\ProductLocalization\ProductLocalization $productLocalization}
<article n:class="b-variant, $class, link-mask">
	<p class="b-variant__img">
		{if $productDto->firstImageObjectMd !== null}
			<img class="img img--4-3" src="{$productDto->firstImageObjectMd->src}" alt="{$productDto->firstImageAlt}" loading="lazy" width="321" height="241" {if $hasVariants}data-product-target="mainImg"{/if}>
		{else}
			<img class="img img--4-3" src="/static/img/illust/noimg.svg" alt="" loading="lazy" width="321" height="241" {if $hasVariants}data-product-target="mainImg"{/if}>
		{/if}
	</p>
	<h2 class="b-variant__title">
		<a href="/{$productDto->alias}" class="b-variant__link link-mask__link">
			{$productDto->nameAnchor}
		</a>
	</h2>
	<p class="b-variant__price">
		{var $priceVat = $productDto->priceVat}
		{*var $price = $productDto->price}
		{var $priceInfo = $productEntity->getPriceInfo($mutation, $priceLevel, $state)*}
		<b>{$priceVat|money}</b> s DPH
	</p>
	<p class="b-variant__info">
		TODO: Nový 2 roky záruka
	</p>
	{include $templates.'/part/core/availability.latte'}
	<dl class="b-variant__parameters" n:if="$packageContentsKeys !== null">
		<span class="b-variant__row" n:foreach="$packageContentsKeys as $key => $name">
			<dt>{$name}</dt><dd>{if isset($productLocalization->getPackageContents()[$key]) && ($rowValue = $productLocalization->getPackageContents()[$key]) !== null}{$rowValue['amount']}&times;{else}-{/if}</dd>
		</span>
		{*<span class="b-variant__row">
			<dt>Baterie</dt><dd>1&times;</dd>
		</span>
		<span class="b-variant__row">
			<dt>Náhradní vrtule</dt><dd>1&times; pár</dd>
		</span>
		<span class="b-variant__row">
			<dt>Kryt gymbalu</dt><dd>1&times;</dd>
		</span>
		<span class="b-variant__row">
			<dt>Šroubky</dt><dd>6&times;</dd>
		</span>
		<span class="b-variant__row">
			<dt>Šroubovák</dt><dd>1&times;</dd>
		</span>
		<span class="b-variant__row">
			<dt>Napájecí USB-C kabel do USB-C</dt><dd>1&times;</dd>
		</span>*}
	</dl>


			{if str_contains($class, 'is-active')}
				{include $templates.'/part/form/add.latte', variant: $variant, showInput: false, btnClass: 'btn--xs link-mask__unmask btn--secondary', listId: $listId, listName: $listName}
			{else}
				<p class="b-variant__btns">
					<a href="/{$productDto->alias}" class="btn link-mask__unmask">
						<span class="btn__text">
							{_"btn_pick"}
						</span>
					</a>
					{include $templates.'/part/form/addToCompare.latte'}
					{*<a href="#" class="b-action link-mask__unmask">
						<span class="b-action__icon">{('compare')|icon}</span>
						<span class="u-vhide">
							{_"btn_compare"}
						</span>
					</a>*}
				</p>
			{/if}
</article>
