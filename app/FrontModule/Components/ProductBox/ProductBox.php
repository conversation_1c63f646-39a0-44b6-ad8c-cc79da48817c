<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductBox;

use App\FrontModule\Components\AddToCart\AddToCart;
use App\FrontModule\Components\AddToCart\AddToCartFactory;
use App\FrontModule\Components\AddToCompareComponent\AddToCompareComponent;
use App\FrontModule\Components\AddToCompareComponent\AddToCompareComponentFactory;
use App\FrontModule\Components\VariantPicker\VariantPickerService;
use App\Model\DTO\Product\ProductDto;
use App\Model\DTO\Product\ProductDtoProvider;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\User\UserRepository;
use App\Model\Setup;
use App\Model\TranslatorDB;
use App\PostType\Tag\Model\Checker\Checker;
use App\PostType\Tag\Model\Checker\CheckerDTO;
use App\PostType\Tag\Model\TagType;
use Nette\Application\UI\Control;

final class ProductBox extends Control
{

	private ProductDto $productDto;

	protected array $params = [
		'titleTag' => null,
		'class' => '',
		'lazyLoading' => true,
		'longLangs' => false,
	];

	public function __construct(
		private readonly Product $product,
		private readonly ?ProductVariant $variant,
		private readonly Setup $setup,
		private readonly array $parametersToTemplate,
		private readonly AddToCartFactory $addToCartFactory,
		private readonly TranslatorDB $translator,
		private readonly ProductDtoProvider $productDtoProvider,
		private readonly Checker $checker,
		private readonly VariantPickerService $variantPickerService,
		private readonly AddToCompareComponentFactory $addToCompareComponentFactory,
		private UserRepository $userRepository,
		private readonly ?string $templateFile = null,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}


	private function init(): void
	{
		$this->productDto = $this->productDtoProvider->get($this->product, $this->variant);
		$this->getTemplate()->setFile(__DIR__ . '/productBox.latte');
		if ($this->templateFile !== null && file_exists(__DIR__ . '/' . $this->templateFile)) {
			$this->getTemplate()->setFile(__DIR__ . '/' . $this->templateFile);
		}
	}

	public function render(mixed $param = null): void
	{
		if (is_array($param)) {
			$this->params = array_merge($this->params, $param);
		}

		$this->getTemplate()->templates = FE_TEMPLATE_DIR;
		$this->getTemplate()->productEntity = $this->product;
		$this->getTemplate()->productDto = $this->productDto;
		$this->getTemplate()->productLocalization = $this->product->getLocalization($this->setup->mutation);
		$this->getTemplate()->priceLevel = $this->setup->priceLevel;
		$this->getTemplate()->state = $this->setup->state;
		$this->getTemplate()->mutation = $this->setup->mutation;
		$this->getTemplate()->lazyLoading = $this->params['lazyLoading'] ?? true;
		$this->getTemplate()->longLangs = $this->params['longLangs'] ?? false;
		$this->getTemplate()->class = $this->params['class'] ?? '';
		$this->getTemplate()->packageContentsKeys = $this->params['packageContentsKeys'] ?? [];
		$this->getTemplate()->variant = $this->product->firstVariant;
		$this->getTemplate()->hasVariants = $this->product->activeVariants->count() > 1;
		$this->getTemplate()->hasLastMinute = false;
		$this->getTemplate()->parametersToTemplate = $this->parametersToTemplate;

		foreach ($this->parametersToTemplate as $key => $value) {
			$this->getTemplate()->$key = $value;
		}

		$colorsPickerParameterLists = [];
		if ($this->product->isCourse()) {
			$this->getTemplate()->hasLastMinute = $this->checker->getCheckerFunction(TagType::lastMinute, $this->setup)(
				new CheckerDTO(productLocalization: $this->product->getLocalization($this->setup->mutation))
			);
		} else {
			if ($this->product->activeVariants->count() > 1) {
				$pickerParameterLists = $this->variantPickerService->getParameterValuesForProduct(
					VariantPickerService::TYPE_PRODUCT_BOX,
					$this->product->getLocalization($this->setup->mutation),
					$this->setup
				);
				$colorsPickerParameterLists = array_values($pickerParameterLists)[0]['items'] ?? [];
				$colorsPickerParameterLists = array_values($colorsPickerParameterLists);
			}
		}
		$this->getTemplate()->colorsPickerParameterLists = $colorsPickerParameterLists;
		$this->getTemplate()->setTranslator($this->translator);

		$this->getTemplate()->render();
	}

	protected function createComponentAddToCart(): AddToCart
	{
		return $this->addToCartFactory->create($this->product, $this->productDto, $this->setup->state, $this->setup->priceLevel);
	}

	public function createComponentAddToCompareComponent(): AddToCompareComponent
	{
		assert($this->product instanceof Product);
		return $this->addToCompareComponentFactory->create($this->product, $this->setup, [
			'from' => 'productBox',
		]);
	}

}
