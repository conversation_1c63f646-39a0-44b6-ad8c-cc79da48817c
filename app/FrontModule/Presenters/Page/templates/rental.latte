{block content}
	{snippet content}
		<div class="row-main">
			{embed $templates.'/part/box/header.latte', object: $object, templates: $templates, translator: $translator}
				{block extra}
					{include $templates.'/part/box/header-top.latte'}
				{/block}
			{/embed}

			{php $stepsCf = $object->cf->steps ?? false}
			{if $stepsCf}
				{embed $templates.'/part/box/steps.latte', class: 'u-mb-sm u-mb-xl@md', items: $stepsCf->steps ?? [], title: $stepsCf->title ?? false, bd: true, object: $object, templates: $templates}
					{block rightExtra}
						<p class="tw-mb-0">
							{* TODO BE: form modal *}
							<a href="#" class="btn btn--secondary btn--lg" data-modal='{"medium": "fetch"}' data-snippetid="snippet--content">
								<span class="btn__text">
									{_"btn_start_picking"}
								</span>
							</a>
						</p>
					{/block}
				{/embed}
			{/if}

			{include $templates.'/part/box/prices.latte'}

			{control customContentRenderer}

			{php $personCtaCf = $object->cf->person_cta ?? false}
			{if $personCtaCf}
				{embed $templates.'/part/box/person-cta.latte', person: isset($personCtaCf->person) ? $personCtaCf->person->getEntitY() ?? false : false, content: $personCtaCf->content ?? false}
					{block content}
						<p class="tw-flex tw-justify-center tw-gap-[0.8rem_1.2rem] tw-flex-wrap">
							{* TODO BE: modal form *}
							<a href="#" class="btn btn--secondary" data-naja-history="off" data-modal='{"medium": "fetch"}' data-snippetid="snippet--content">
								<span class="btn__text">
									<span class="btn__inner">
										{_"btn_start_picking"}
									</span>
								</span>
							</a>
						</p>
					{/block}
				{/embed}
			{/if}


			{include $templates.'/part/crossroad/dummy-articles-carousel.latte', gradient: true, title: $translator->translate('title_articles_recommended')}

			<hr class="u-mb-sm u-mb-md@md">
			{include $templates.'/part/box/benefits.latte'}
		</div>
	{/snippet}
{/block}
