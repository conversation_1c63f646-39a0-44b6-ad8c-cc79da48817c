{default $class = false}
{default $isLg = str_contains($class, 'b-compare-product--lg')}
{default $dummyLongText = false}
{default $product}
{var $productDto = $productDtos[$product->id]}

<article n:class="b-compare-product, $class, 'tw-flex tw-flex-col tw-p-[1.2rem_1.6rem] tw-border-solid tw-border-[0.1rem] tw-border-tile tw-rounded-lg link-mask tw-h-full', $isLg ? 'md:tw-p-[2.4rem] md:tw-rounded-xl'">
	<div n:class="'tw-flex tw-flex-1 tw-gap-[0_1.2rem]', $isLg ? 'md:tw-flex-col md:tw-gap-0'">
		<p n:class="'tw-mb-[0] tw-max-w-[7.6rem]', $isLg ? 'md:tw-max-w-[15rem] md:tw-mx-auto md:tw-mb-[1.2rem]'">
			{var $img = $product->firstImage?->getSize('lg')}
			{if $img}
				<img class="img img--4-3" src="{$img->src}" alt="{$product->nameAnchor}" loading="lazy">
			{else}
				<img class="img img--4-3" src="/static/img/illust/noimg.svg" alt="{$product->nameAnchor}" loading="lazy">
			{/if}
		</p>
		<div n:class="'u-mb-last-0 tw-mb-[0.4rem] tw-flex-1 tw-flex tw-flex-col', $isLg ? 'md:tw-mb-[1.6rem]'">
			<h3 n:class="'tw-mb-[0] tw-mt-0 tw-text-[1.3rem] tw-leading-[1.4]', $isLg ? 'md:tw-text-[1.6rem] md:tw-mb-[0.8rem] md:tw-leading-[1.6]'">
				<a href="{plink $product}" class="b-compare-product__link link-mask__link tw-no-underline tw-line-clamp-2">
					{$product->name}
				</a>
			</h3>
			<p n:class="'tw-mb-[0] tw-text-[1.2rem] tw-leading-[1.4] tw-line-clamp-2', $isLg ? 'md:tw-text-[1.3rem] md:tw-mb-[1.2rem] md:tw-leading-[1.6]'">
				{$product->annotation|texy, clean: true|noescape}
			</p>
			{include $templates.'/part/core/availability.latte', class: 'tw-text-[1.2rem]', isDetail: false, productDto: $productDto}
		</div>
	</div>
	<div class="b-compare-product__bottom tw-flex tw-justify-between tw-items-center tw-mb-0 tw-gap-[1.2rem] md:tw-contents md:group-[.is-sticky-top]/toolbar:tw-flex">
		<div class="max-md:tw-contents tw-flex tw-gap-[1.2rem] tw-justify-between tw-items-center md:tw-gap-[0.4rem] md:group-[.is-sticky-top]/toolbar:tw-contents">
			<p class="tw-mb-0">
				<a n:href="removeFromCompare! productId => $product->id, currentCategory => $currentCategory" data-naja="" data-naja-history="off" data-naja-loader="body" class="ajax b-compare-product__link tw-flex tw-items-center tw-gap-[1rem] link-mask__unmask tw-no-underline tw-text-[1.3rem]">
					<span class="btn btn--icon btn--gray btn--bd">
						<span class="btn__text">
							{('cross')|icon, 'btn__icon'}
						</span>
					</span>
					<span class="group-[.is-sticky-top]/toolbar:tw-hidden">
						{_"btn_compare_remove"}
					</span>
				</a>
			</p>
			<p class="tw-mb-0">
				<a href="{plink $product}" class="btn link-mask__unmask">
					<span class="btn__text">
						{_"btn_buy"}
					</span>
				</a>
			</p>
		</div>
		<p class="tw-flex tw-gap-[0.4rem] tw-mb-0 tw-justify-center md:tw-order-[-1] md:tw-mb-[0.8rem] md:tw-gap-[0.8rem] md:group-[.is-sticky-top]/toolbar:tw-gap-[0.4rem] md:group-[.is-sticky-top]/toolbar:tw-order-[1] md:group-[.is-sticky-top]/toolbar:tw-mb-0">
			<a n:href="moveProduct! $currentIndex, 'up', $currentCategory" data-naja="" data-naja-history="off" data-naja-loader="body" class="btn btn--bd btn--icon btn--gray link-mask__unmask">
				<span class="btn__text">
					{('angle-left')|icon, 'btn__icon max-md:tw-rotate-90'}
					<span class="u-vhide">{_"compare_move_left"}</span>
				</span>
			</a>
			<a n:href="moveProduct! $currentIndex, 'down', $currentCategory" data-naja="" data-naja-history="off" data-naja-loader="body" class="btn btn--bd btn--icon btn--gray link-mask__unmask">
				<span class="btn__text">
					{('angle-right')|icon, 'btn__icon max-md:tw-rotate-90'}
					<span class="u-vhide">{_"compare_move_right"}</span>
				</span>
			</a>
		</p>
	</div>
</article>