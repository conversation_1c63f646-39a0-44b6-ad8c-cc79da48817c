{default $class = 'u-mb-sm'}

{snippet filterSelected}
	<div n:if="($filter->nonRoot ?? false) && count($filter->boxes ?? [])" n:class="b-filters, $class">
		<ul class="b-filters__list">

			{dump $filter->boxes}
			{foreach $filter->boxes as $box}
				{varType App\Model\BucketFilter\Box\CheckBoxes $box}
				{if $box->getCheckedItems()}
					{if $box instanceOf App\Model\BucketFilter\Box\Slider}
						{capture $link}{link 'this', filter => $box->filterToDeSelect, 'filter_set'=> 1}{/capture}
						{php $link = urldecode(htmlspecialchars_decode($link))}
						{var $value = $box}
						<li class="b-filters__item">
							<span class="b-filters__name">{_$box->title}: </span>
							<a href="{$link}" class="flag flag--remove" data-naja data-naja-loader="body">
								{if $box->inputValueMin != $box->selectedMin && $box->inputValueMax != $box->selectedMax}
									{$box->format($box->selectedMin)}{if $box->unit} {$box->unit}{/if} &ndash; {$box->format($box->selectedMax)}{if $box->unit} {$box->unit}{/if}
								{elseif $box->inputValueMin != $box->selectedMin}
									{_from} {$box->format($box->selectedMin)}{if $box->unit} {$box->unit}{/if}
								{elseif $box->inputValueMax != $box->selectedMax}
									{_to} {$box->format($box->selectedMax)}{if $box->unit} {$box->unit}{/if}
								{/if}
							{('cross')|icon, 'flag__icon'}
							</a>
						</li>

					{else}
					{var $isFlagFilter = isset($filter->boxesByNamespace['flags']) && in_array($box, $filter->boxesByNamespace['flags'])}
						<li class="b-filters__item">
							<span n:if="!$isFlagFilter" class="b-filters__name">{_$box->title}: </span>
							<ul class="b-filters__list">
								<li n:foreach="$box->getCheckedItems() as $value" class="b-filters__item">
									{capture $link}{link 'this', filter => $value->filterToDeSelect, 'filter_set'=> 1}{/capture}
								{php $link = urldecode(htmlspecialchars_decode($link))}
									<a href="{$link}" class="flag flag--remove" data-naja data-naja-loader="body">
										{$value->name}
									{if $box->unit}
											{$box->unit}
										{/if}
									{('cross')|icon, 'flag__icon'}
									</a>
								</li>
							</ul>
						</li>
					{/if}
				{/if}
			{/foreach}

		{* Remove all filters *}
			<li class="b-filters__item u-no-print">
				{capture $link}{link 'this', 'filter' => $filter->followingCleanFilterParameters, 'pager-page' => null, 'filter_clear'=> 1}{/capture}
			{php $link = urldecode(htmlspecialchars_decode($link))}
				<a href="{$link}" class="b-filters__reset" data-naja data-naja-loader="body">
					{_"btn_filter_remove"}
				</a>
			</li>
		</ul>
	</div>
{/snippet}
