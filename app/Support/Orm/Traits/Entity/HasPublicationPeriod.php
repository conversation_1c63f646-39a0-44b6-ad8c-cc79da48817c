<?php

declare(strict_types = 1);

namespace App\Support\Orm\Traits\Entity;

use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property DateTimeImmutable|null $publicFrom {default now}
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 */
trait HasPublicationPeriod
{

	public function setPublicFrom(?DateTimeImmutable $publicFrom): void
	{
		$this->publicFrom = $publicFrom;
	}

	public function getPublicFrom(): DateTimeImmutable|null
	{
		return $this->publicFrom;
	}

	public function setPublicTo(?DateTimeImmutable $publicTo): void
	{
		$this->publicTo = $publicTo;
	}

	public function getPublicTo(): DateTimeImmutable|null
	{
		return $this->publicTo;
	}
}
