SET NAMES utf8mb4;

INSERT INTO tree_parent VALUES ();

SET @lastParentId=LAST_INSERT_ID();

INSERT INTO tree (mutationId, rootId, extId, treeParentId, parentId, level, path, sort, last, uid, created, createdTime, createdTimeOrder, edited, editedTime, template, type, publicFrom, publicTo, name, nameAnchor, nameAnchorBreadcrumb, nameTitle, nameShort, nameHeading, description, keywords, public, score, forceNoIndex, hideInSearch, hideInSitemap, hideInMenu, annotation, content, hideFirstImage, links, seoTitleFilter, seoAnnotationFilter, seoDescriptionFilter, videos, customFieldsJson, customContentJson, productAttachedId, hasLinkedCategories) VALUES
 ( 1, 1, null, @lastParentId, 43, 2, '1|43|', 7, 1, 'sizes', 0, '2025-03-25 13:52:28', '2025-03-25 13:52:00', 3, '2025-03-25 13:54:12', ':Front:Page:default', 'common', '2025-03-25 13:52:00', '2125-03-25 13:52:00', 'Velikosti', 'Velikosti', '', 'Velikosti', '', '', '', '', 1, 0, 1, 1, 0, 0, '', '', null, null, null, null, null, null, '{}', '{}', null, null);

SET @lastPageId=LAST_INSERT_ID();
INSERT INTO `alias` ( `alias`, `module`, `referenceId`, `mutationId`) VALUES
	('velikosti',	'tree',	@lastPageId,	1);
